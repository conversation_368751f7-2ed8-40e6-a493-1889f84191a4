/**
 * Courses Redux Slice
 * 
 * Manages course data, enrollment, favorites, and course-related state.
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

// Types
export interface Instructor {
  id: number;
  name: string;
  email: string;
  avatar?: string;
  bio?: string;
  rating?: number;
}

export interface Course {
  id: number;
  title: string;
  description: string;
  code: string;
  credits: number;
  level: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED';
  instructor: Instructor;
  enrollment_count: number;
  max_students: number;
  duration: string;
  rating: number;
  thumbnail?: string;
  tags: string[];
  price: number;
  is_enrolled: boolean;
  is_favorite: boolean;
  progress: number;
  created_at: string;
  updated_at: string;
  start_date?: string;
  end_date?: string;
  category: string;
  prerequisites?: string[];
  learning_outcomes?: string[];
}

export interface CourseFilters {
  category?: string;
  level?: string;
  instructor?: string;
  priceRange?: [number, number];
  rating?: number;
  tags?: string[];
  search?: string;
}

export interface CoursesState {
  courses: Course[];
  enrolledCourses: Course[];
  favoriteCourses: Course[];
  currentCourse: Course | null;
  loading: boolean;
  error: string | null;
  filters: CourseFilters;
  pagination: {
    page: number;
    totalPages: number;
    totalCount: number;
    pageSize: number;
  };
  categories: string[];
  tags: string[];
}

// Initial state
const initialState: CoursesState = {
  courses: [],
  enrolledCourses: [],
  favoriteCourses: [],
  currentCourse: null,
  loading: false,
  error: null,
  filters: {},
  pagination: {
    page: 1,
    totalPages: 1,
    totalCount: 0,
    pageSize: 12,
  },
  categories: [],
  tags: [],
};

// Async thunks
export const fetchCourses = createAsyncThunk(
  'courses/fetchCourses',
  async (params: { page?: number; filters?: CourseFilters } = {}, { rejectWithValue }) => {
    try {
      const queryParams = new URLSearchParams();
      
      if (params.page) queryParams.append('page', params.page.toString());
      if (params.filters?.search) queryParams.append('search', params.filters.search);
      if (params.filters?.category) queryParams.append('category', params.filters.category);
      if (params.filters?.level) queryParams.append('level', params.filters.level);

      const response = await fetch(`/api/v1/courses?${queryParams}`);
      
      if (!response.ok) {
        return rejectWithValue('Failed to fetch courses');
      }

      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Network error');
    }
  }
);

export const fetchCourseById = createAsyncThunk(
  'courses/fetchCourseById',
  async (courseId: number, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/v1/courses/${courseId}/`);
      
      if (!response.ok) {
        return rejectWithValue('Failed to fetch course');
      }

      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Network error');
    }
  }
);

export const enrollInCourse = createAsyncThunk(
  'courses/enrollInCourse',
  async (courseId: number, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/v1/courses/${courseId}/enroll/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.message || 'Enrollment failed');
      }

      const data = await response.json();
      return { courseId, ...data };
    } catch (error: any) {
      return rejectWithValue(error.message || 'Network error');
    }
  }
);

export const toggleCourseFavorite = createAsyncThunk(
  'courses/toggleCourseFavorite',
  async (courseId: number, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/v1/courses/${courseId}/favorite/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        return rejectWithValue('Failed to toggle favorite');
      }

      const data = await response.json();
      return { courseId, is_favorite: data.is_favorite };
    } catch (error: any) {
      return rejectWithValue(error.message || 'Network error');
    }
  }
);

export const fetchEnrolledCourses = createAsyncThunk(
  'courses/fetchEnrolledCourses',
  async (_, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/v1/courses/enrolled/', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        return rejectWithValue('Failed to fetch enrolled courses');
      }

      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Network error');
    }
  }
);

export const fetchFavoriteCourses = createAsyncThunk(
  'courses/fetchFavoriteCourses',
  async (_, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/v1/courses/favorites/', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        return rejectWithValue('Failed to fetch favorite courses');
      }

      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Network error');
    }
  }
);

// Courses slice
const coursesSlice = createSlice({
  name: 'courses',
  initialState,
  reducers: {
    setFilters: (state, action: PayloadAction<CourseFilters>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = {};
    },
    setCurrentCourse: (state, action: PayloadAction<Course | null>) => {
      state.currentCourse = action.payload;
    },
    updateCourseProgress: (state, action: PayloadAction<{ courseId: number; progress: number }>) => {
      const { courseId, progress } = action.payload;
      
      // Update in all relevant arrays
      const updateCourse = (course: Course) => {
        if (course.id === courseId) {
          course.progress = progress;
        }
      };

      state.courses.forEach(updateCourse);
      state.enrolledCourses.forEach(updateCourse);
      state.favoriteCourses.forEach(updateCourse);
      
      if (state.currentCourse?.id === courseId) {
        state.currentCourse.progress = progress;
      }
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch courses
    builder
      .addCase(fetchCourses.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCourses.fulfilled, (state, action) => {
        state.loading = false;
        state.courses = action.payload.results || action.payload;
        state.pagination = {
          page: action.payload.page || 1,
          totalPages: action.payload.total_pages || 1,
          totalCount: action.payload.count || action.payload.length || 0,
          pageSize: action.payload.page_size || 12,
        };
      })
      .addCase(fetchCourses.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // Fetch course by ID
    builder
      .addCase(fetchCourseById.fulfilled, (state, action) => {
        state.currentCourse = action.payload;
        
        // Update course in courses array if it exists
        const index = state.courses.findIndex(course => course.id === action.payload.id);
        if (index !== -1) {
          state.courses[index] = action.payload;
        }
      })
      .addCase(fetchCourseById.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Enroll in course
    builder
      .addCase(enrollInCourse.fulfilled, (state, action) => {
        const courseId = action.payload.courseId;
        
        // Update enrollment status in all relevant arrays
        const updateEnrollment = (course: Course) => {
          if (course.id === courseId) {
            course.is_enrolled = true;
            course.enrollment_count += 1;
          }
        };

        state.courses.forEach(updateEnrollment);
        state.favoriteCourses.forEach(updateEnrollment);
        
        if (state.currentCourse?.id === courseId) {
          state.currentCourse.is_enrolled = true;
          state.currentCourse.enrollment_count += 1;
        }
      })
      .addCase(enrollInCourse.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Toggle favorite
    builder
      .addCase(toggleCourseFavorite.fulfilled, (state, action) => {
        const { courseId, is_favorite } = action.payload;
        
        // Update favorite status in all relevant arrays
        const updateFavorite = (course: Course) => {
          if (course.id === courseId) {
            course.is_favorite = is_favorite;
          }
        };

        state.courses.forEach(updateFavorite);
        state.enrolledCourses.forEach(updateFavorite);
        
        if (state.currentCourse?.id === courseId) {
          state.currentCourse.is_favorite = is_favorite;
        }

        // Update favorite courses array
        if (is_favorite) {
          const course = state.courses.find(c => c.id === courseId);
          if (course && !state.favoriteCourses.find(c => c.id === courseId)) {
            state.favoriteCourses.push(course);
          }
        } else {
          state.favoriteCourses = state.favoriteCourses.filter(c => c.id !== courseId);
        }
      });

    // Fetch enrolled courses
    builder
      .addCase(fetchEnrolledCourses.fulfilled, (state, action) => {
        state.enrolledCourses = action.payload.results || action.payload;
      });

    // Fetch favorite courses
    builder
      .addCase(fetchFavoriteCourses.fulfilled, (state, action) => {
        state.favoriteCourses = action.payload.results || action.payload;
      });
  },
});

export const {
  setFilters,
  clearFilters,
  setCurrentCourse,
  updateCourseProgress,
  clearError,
} = coursesSlice.actions;

export default coursesSlice.reducer;
