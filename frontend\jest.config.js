module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@components/(.*)$': '<rootDir>/src/components/$1',
    '^@services/(.*)$': '<rootDir>/src/services/$1',
    '^@hooks/(.*)$': '<rootDir>/src/hooks/$1',
    '^@utils/(.*)$': '<rootDir>/src/utils/$1',
    '^@contexts/(.*)$': '<rootDir>/src/contexts/$1',
    '^@features/(.*)$': '<rootDir>/src/features/$1',
    '^@store/(.*)$': '<rootDir>/src/store/$1',
    '^@config/(.*)$': '<rootDir>/src/config/$1',
    '\\.(css|less|scss|sass)$': '<rootDir>/src/__mocks__/cssModule.js',
    '\\.(css|less|scss|sass)': '<rootDir>/src/__mocks__/cssModule.js',
    '^react-toastify/dist/ReactToastify\\.css$': '<rootDir>/src/__mocks__/cssModule.js',
    'react-toastify/dist/ReactToastify.css': '<rootDir>/src/__mocks__/cssModule.js',
    '^@mui/material/styles$':
      '<rootDir>/src/tests/__mocks__/@mui/material/styles/index.js',
    '^@mui/material$': '<rootDir>/src/tests/__mocks__/@mui/material/index.js',
  },
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
  testMatch: ['**/__tests__/**/*.ts?(x)', '**/?(*.)+(spec|test).ts?(x)'],
  transform: {
    '^.+\\.(ts|tsx|js|jsx)$': 'babel-jest', // Transform both TS and JS files
  },
  transformIgnorePatterns: [
    // Don't ignore any files in node_modules that need transformation
    '/node_modules/(?!(@mui|react-icons|framer-motion)/)/',
  ],
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  // Coverage configuration
  collectCoverage: false, // Set to true when running with --coverage
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/tests/**/*',
    '!src/**/__tests__/**/*',
    '!src/**/__mocks__/**/*',
    '!src/main.tsx',
    '!src/vite-env.d.ts',
    '!**/node_modules/**',
    '!**/dist/**',
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html', 'json'],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70,
    },
  },
  // Add globals for import.meta.env
  globals: {
    'import.meta': {
      env: {
        MODE: 'test',
        VITE_API_URL: 'http://localhost:8000',
        VITE_ENABLE_HMR: 'false',
      },
    },
  },
  // Test timeout
  testTimeout: 10000,
  // Verbose output
  verbose: true,
};
