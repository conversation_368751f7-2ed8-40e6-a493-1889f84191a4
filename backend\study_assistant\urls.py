from django.urlsimportincludepathfromrest_framework.routersimportDefaultRouterfrom.viewsimport(PracticeQuestionViewSetSpacedRepetitionViewSetStudyAssistantViewSetStudySessionViewSetStudyTopicViewSet)router=DefaultRouter()router.register(r"sessions"StudySessionViewSetbasename="study-session")router.register(r"topics"StudyTopicViewSetbasename="study-topic")router.register(r"assistant"StudyAssistantViewSetbasename="study-assistant")router.register(r"spaced-repetition"SpacedRepetitionViewSetbasename="spaced-repetition")router.register(r"practice-questions"PracticeQuestionViewSetbasename="practice-question")urlpatterns=[path(""include(router.urls))]