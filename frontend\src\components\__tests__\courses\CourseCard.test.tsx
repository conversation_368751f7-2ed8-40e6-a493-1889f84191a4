/**
 * Comprehensive tests for CourseCard component
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Provider } from 'react-redux';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import { configureStore } from '@reduxjs/toolkit';

import CourseCard from '../../courses/CourseCard';
import { theme } from '../../../theme';
import coursesSlice from '../../../store/slices/coursesSlice';
import authSlice from '../../../store/slices/authSlice';

// Mock course data
const mockCourse = {
  id: 1,
  title: 'Introduction to React',
  description: 'Learn the fundamentals of React development',
  code: 'REACT101',
  credits: 3,
  level: 'BEGINNER',
  instructor: {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
  },
  enrollment_count: 25,
  max_students: 30,
  duration: '8 weeks',
  rating: 4.5,
  thumbnail: '/images/react-course.jpg',
  tags: ['React', 'JavaScript', 'Frontend'],
  price: 99.99,
  is_enrolled: false,
  is_favorite: false,
  progress: 0,
};

// Create test store
const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      courses: coursesSlice,
      auth: authSlice,
    },
    preloadedState: {
      courses: {
        courses: [mockCourse],
        loading: false,
        error: null,
        ...initialState.courses,
      },
      auth: {
        user: { id: 1, username: 'testuser', role: 'STUDENT' },
        isAuthenticated: true,
        ...initialState.auth,
      },
    },
  });
};

// Test wrapper
const TestWrapper: React.FC<{ children: React.ReactNode; store?: any }> = ({
  children,
  store = createTestStore(),
}) => (
  <Provider store={store}>
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        {children}
      </ThemeProvider>
    </BrowserRouter>
  </Provider>
);

describe('CourseCard Component', () => {
  it('renders course information correctly', () => {
    render(
      <TestWrapper>
        <CourseCard course={mockCourse} />
      </TestWrapper>
    );

    expect(screen.getByText('Introduction to React')).toBeInTheDocument();
    expect(screen.getByText('Learn the fundamentals of React development')).toBeInTheDocument();
    expect(screen.getByText('REACT101')).toBeInTheDocument();
    expect(screen.getByText('3 Credits')).toBeInTheDocument();
    expect(screen.getByText('BEGINNER')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
  });

  it('displays enrollment information', () => {
    render(
      <TestWrapper>
        <CourseCard course={mockCourse} />
      </TestWrapper>
    );

    expect(screen.getByText('25/30 Students')).toBeInTheDocument();
    expect(screen.getByText('8 weeks')).toBeInTheDocument();
  });

  it('shows rating with stars', () => {
    render(
      <TestWrapper>
        <CourseCard course={mockCourse} />
      </TestWrapper>
    );

    expect(screen.getByText('4.5')).toBeInTheDocument();
    // Check for star icons (assuming you use star icons for rating)
    const stars = screen.getAllByTestId('star-icon');
    expect(stars).toHaveLength(5); // Assuming 5-star rating system
  });

  it('displays course tags', () => {
    render(
      <TestWrapper>
        <CourseCard course={mockCourse} />
      </TestWrapper>
    );

    expect(screen.getByText('React')).toBeInTheDocument();
    expect(screen.getByText('JavaScript')).toBeInTheDocument();
    expect(screen.getByText('Frontend')).toBeInTheDocument();
  });

  it('shows price information', () => {
    render(
      <TestWrapper>
        <CourseCard course={mockCourse} />
      </TestWrapper>
    );

    expect(screen.getByText('$99.99')).toBeInTheDocument();
  });

  it('handles enrollment action', async () => {
    const user = userEvent.setup();
    const mockEnroll = jest.fn();
    
    render(
      <TestWrapper>
        <CourseCard course={mockCourse} onEnroll={mockEnroll} />
      </TestWrapper>
    );

    const enrollButton = screen.getByRole('button', { name: /enroll/i });
    await user.click(enrollButton);

    expect(mockEnroll).toHaveBeenCalledWith(mockCourse.id);
  });

  it('shows different button for enrolled course', () => {
    const enrolledCourse = { ...mockCourse, is_enrolled: true, progress: 45 };
    
    render(
      <TestWrapper>
        <CourseCard course={enrolledCourse} />
      </TestWrapper>
    );

    expect(screen.getByRole('button', { name: /continue/i })).toBeInTheDocument();
    expect(screen.getByText('45% Complete')).toBeInTheDocument();
  });

  it('handles favorite toggle', async () => {
    const user = userEvent.setup();
    const mockToggleFavorite = jest.fn();
    
    render(
      <TestWrapper>
        <CourseCard course={mockCourse} onToggleFavorite={mockToggleFavorite} />
      </TestWrapper>
    );

    const favoriteButton = screen.getByRole('button', { name: /add to favorites/i });
    await user.click(favoriteButton);

    expect(mockToggleFavorite).toHaveBeenCalledWith(mockCourse.id);
  });

  it('shows favorited state', () => {
    const favoriteCourse = { ...mockCourse, is_favorite: true };
    
    render(
      <TestWrapper>
        <CourseCard course={favoriteCourse} />
      </TestWrapper>
    );

    const favoriteButton = screen.getByRole('button', { name: /remove from favorites/i });
    expect(favoriteButton).toHaveClass('favorited'); // Assuming CSS class for favorited state
  });

  it('navigates to course detail on click', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CourseCard course={mockCourse} />
      </TestWrapper>
    );

    const courseCard = screen.getByTestId('course-card');
    await user.click(courseCard);

    expect(window.location.pathname).toBe(`/courses/${mockCourse.id}`);
  });

  it('displays course thumbnail', () => {
    render(
      <TestWrapper>
        <CourseCard course={mockCourse} />
      </TestWrapper>
    );

    const thumbnail = screen.getByAltText('Introduction to React');
    expect(thumbnail).toBeInTheDocument();
    expect(thumbnail).toHaveAttribute('src', mockCourse.thumbnail);
  });

  it('handles missing thumbnail gracefully', () => {
    const courseWithoutThumbnail = { ...mockCourse, thumbnail: null };
    
    render(
      <TestWrapper>
        <CourseCard course={courseWithoutThumbnail} />
      </TestWrapper>
    );

    // Should show placeholder or default image
    const placeholder = screen.getByTestId('course-placeholder');
    expect(placeholder).toBeInTheDocument();
  });

  it('shows loading state during enrollment', async () => {
    const user = userEvent.setup();
    const mockEnroll = jest.fn(() => new Promise(resolve => setTimeout(resolve, 1000)));
    
    render(
      <TestWrapper>
        <CourseCard course={mockCourse} onEnroll={mockEnroll} />
      </TestWrapper>
    );

    const enrollButton = screen.getByRole('button', { name: /enroll/i });
    await user.click(enrollButton);

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    expect(screen.getByText(/enrolling/i)).toBeInTheDocument();
  });

  it('displays course level with appropriate styling', () => {
    const beginnerCourse = { ...mockCourse, level: 'BEGINNER' };
    const intermediateCourse = { ...mockCourse, level: 'INTERMEDIATE' };
    const advancedCourse = { ...mockCourse, level: 'ADVANCED' };

    const { rerender } = render(
      <TestWrapper>
        <CourseCard course={beginnerCourse} />
      </TestWrapper>
    );

    expect(screen.getByText('BEGINNER')).toHaveClass('level-beginner');

    rerender(
      <TestWrapper>
        <CourseCard course={intermediateCourse} />
      </TestWrapper>
    );

    expect(screen.getByText('INTERMEDIATE')).toHaveClass('level-intermediate');

    rerender(
      <TestWrapper>
        <CourseCard course={advancedCourse} />
      </TestWrapper>
    );

    expect(screen.getByText('ADVANCED')).toHaveClass('level-advanced');
  });

  it('shows enrollment deadline if available', () => {
    const courseWithDeadline = {
      ...mockCourse,
      enrollment_deadline: '2024-12-31T23:59:59Z',
    };
    
    render(
      <TestWrapper>
        <CourseCard course={courseWithDeadline} />
      </TestWrapper>
    );

    expect(screen.getByText(/enrollment deadline/i)).toBeInTheDocument();
    expect(screen.getByText(/december 31, 2024/i)).toBeInTheDocument();
  });

  it('disables enrollment for full courses', () => {
    const fullCourse = {
      ...mockCourse,
      enrollment_count: 30,
      max_students: 30,
    };
    
    render(
      <TestWrapper>
        <CourseCard course={fullCourse} />
      </TestWrapper>
    );

    const enrollButton = screen.getByRole('button', { name: /course full/i });
    expect(enrollButton).toBeDisabled();
  });

  it('shows prerequisite information', () => {
    const courseWithPrereqs = {
      ...mockCourse,
      prerequisites: ['HTML Basics', 'CSS Fundamentals'],
    };
    
    render(
      <TestWrapper>
        <CourseCard course={courseWithPrereqs} />
      </TestWrapper>
    );

    expect(screen.getByText(/prerequisites/i)).toBeInTheDocument();
    expect(screen.getByText('HTML Basics')).toBeInTheDocument();
    expect(screen.getByText('CSS Fundamentals')).toBeInTheDocument();
  });

  it('is accessible', () => {
    render(
      <TestWrapper>
        <CourseCard course={mockCourse} />
      </TestWrapper>
    );

    // Check for proper ARIA labels
    expect(screen.getByRole('article')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /enroll/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /add to favorites/i })).toBeInTheDocument();

    // Check for proper heading structure
    expect(screen.getByRole('heading', { level: 3 })).toBeInTheDocument();
  });

  it('handles keyboard navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CourseCard course={mockCourse} />
      </TestWrapper>
    );

    // Tab through interactive elements
    await user.tab();
    expect(screen.getByTestId('course-card')).toHaveFocus();

    await user.tab();
    expect(screen.getByRole('button', { name: /add to favorites/i })).toHaveFocus();

    await user.tab();
    expect(screen.getByRole('button', { name: /enroll/i })).toHaveFocus();
  });

  it('supports different card sizes', () => {
    const { rerender } = render(
      <TestWrapper>
        <CourseCard course={mockCourse} size="small" />
      </TestWrapper>
    );

    expect(screen.getByTestId('course-card')).toHaveClass('course-card-small');

    rerender(
      <TestWrapper>
        <CourseCard course={mockCourse} size="large" />
      </TestWrapper>
    );

    expect(screen.getByTestId('course-card')).toHaveClass('course-card-large');
  });
});
