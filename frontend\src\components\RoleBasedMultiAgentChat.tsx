import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Box,
  Typography,
  TextField,
  Chip,
  Paper,
  CircularProgress,
  alpha,
  List,
  ListItem,
  ListItemText,
  Button,
  useTheme,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Delete as DeleteIcon,
  Add as AddIcon,
  AutoAwesome,
  History as HistoryIcon,
} from '@mui/icons-material';
import { FiSend } from 'react-icons/fi';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { styled } from '@mui/material/styles';
import { RootState } from '../app/store';
import multiAgentService from '../services/multiAgentService';
import multiAgentConversationService, {
  MultiAgentConversation,
  MultiAgentMessage,
} from '../services/multiAgentConversationService';
import ChatModeTabNavigation from './chat/ChatModeTabNavigation';

// 🎭 Role-Based Multi-Agent Chat Component
// Adapts to user roles: <PERSON><PERSON><PERSON>, PROF<PERSON>SOR, STUDENT

interface RoleConfig {
  name: string;
  code: string;
  color: string;
  icon: string;
  agents: AgentInfo[];
  sampleMessages: string[];
  description: string;
}

interface AgentInfo {
  name: string;
  icon: string;
  color: string;
  description: string;
  keywords: string[];
}

interface ChatMessage {
  id: string;
  content: string;
  isUser: boolean;
  agentUsed?: string;
  timestamp: Date;
  metadata?: any;
}

// Styled Components
const SidebarPaper = styled(Paper)(({ theme }) => {
  if (!theme || !theme.palette || !theme.spacing) {
    console.error('Theme is not properly defined in SidebarPaper');
    return {};
  }
  
  return {
    width: 320,
    minWidth: 320,
    height: '100%',
    borderRadius: theme.spacing(2),
    borderInlineEnd: `1px solid ${theme.palette.divider || '#e0e0e0'}`,
    background: theme.palette.mode === 'dark' 
      ? `linear-gradient(135deg, ${alpha(theme.palette.background?.paper || '#424242', 0.95)} 0%, ${alpha(theme.palette.background?.default || '#303030', 0.95)} 100%)`
      : `linear-gradient(135deg, ${alpha(theme.palette.background?.paper || '#ffffff', 0.9)} 0%, ${alpha('#f5f7fa', 0.9)} 100%)`,
    backdropFilter: 'blur(20px)',
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',
    border: `1px solid ${alpha(theme.palette.divider || '#e0e0e0', 0.1)}`,
    boxShadow: theme.palette.mode === 'dark' 
      ? `0 8px 32px ${alpha('#000', 0.3)}`
      : `0 8px 32px ${alpha('#000', 0.1)}`,
  };
});

const StyledListItem = styled(ListItem)(({ theme }) => ({
  margin: theme.spacing(0.5, 1),
  borderRadius: theme.spacing(1.5),
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  cursor: 'pointer',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    [theme.direction === 'rtl' ? 'right' : 'left']: 0,
    width: '3px',
    height: '100%',
    background: 'transparent',
    transition: 'all 0.3s ease',
    borderRadius: '0 3px 3px 0',
  },
  '&:hover': {
    background: alpha(theme.palette.primary.main, 0.08),
    transform: theme.direction === 'rtl' ? 'translateX(-4px)' : 'translateX(4px)',
    '&::before': {
      background: alpha(theme.palette.primary.main, 0.3),
    },
  },
  '&.Mui-selected': {
    background: theme.palette.mode === 'dark'
      ? `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.15)} 0%, ${alpha(theme.palette.secondary.main, 0.15)} 100%)`
      : `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.12)} 0%, ${alpha(theme.palette.secondary.main, 0.12)} 100%)`,
    '&::before': {
      background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
    },
    '&:hover': {
      background: theme.palette.mode === 'dark'
        ? `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.2)} 0%, ${alpha(theme.palette.secondary.main, 0.2)} 100%)`
        : `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.18)} 0%, ${alpha(theme.palette.secondary.main, 0.18)} 100%)`,
    },
  },
}));

const CreateButton = styled(Button)(({ theme }) => ({
  margin: theme.spacing(1),
  background: theme.palette.mode === 'dark'
    ? `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`
    : `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
  color: theme.palette.primary.contrastText,
  borderRadius: theme.spacing(2),
  textTransform: 'none',
  fontWeight: 600,
  padding: theme.spacing(1.5, 3),
  boxShadow: theme.palette.mode === 'dark'
    ? `0 4px 16px ${alpha(theme.palette.primary.main, 0.3)}`
    : `0 4px 16px ${alpha(theme.palette.primary.main, 0.2)}`,
  '&:hover': {
    background: theme.palette.mode === 'dark'
      ? `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.9)} 0%, ${alpha(theme.palette.secondary.main, 0.9)} 100%)`
      : `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.9)} 0%, ${alpha(theme.palette.secondary.main, 0.9)} 100%)`,
    transform: 'translateY(-2px)',
    boxShadow: theme.palette.mode === 'dark'
      ? `0 6px 20px ${alpha(theme.palette.primary.main, 0.4)}`
      : `0 6px 20px ${alpha(theme.palette.primary.main, 0.3)}`,
  },
}));

const roleConfigs: RoleConfig[] = [
  {
    name: 'Student',
    code: 'STUDENT',
    color: '#2196F3',
    icon: '🎓',
    description: 'Access tutoring, learning support, and career guidance',
    agents: [
      {
        name: 'General Tutor',
        icon: '🎓',
        color: '#2196F3',
        description: 'Get help with any subject',
        keywords: ['help', 'explain', 'learn'],
      },
      {
        name: 'Math Tutor',
        icon: '🔢',
        color: '#4CAF50',
        description: 'Mathematics and problem solving',
        keywords: ['math', 'equation', 'calculate'],
      },
      {
        name: 'Science Tutor',
        icon: '🔬',
        color: '#FF9800',
        description: 'Science concepts and experiments',
        keywords: ['science', 'biology', 'chemistry'],
      },
      {
        name: 'Language Tutor',
        icon: '📝',
        color: '#9C27B0',
        description: 'Writing and language arts',
        keywords: ['write', 'essay', 'grammar'],
      },
      {
        name: 'Career Advisor',
        icon: '🎯',
        color: '#F44336',
        description: 'Academic and career guidance',
        keywords: ['career', 'advice', 'guidance'],
      },
    ],
    sampleMessages: [
      'Help me understand quadratic equations',
      'Explain photosynthesis process',
      'How do I write a better essay?',
      'What career path should I choose?',
      'I need help with my homework',
    ],
  },
  {
    name: 'Professor',
    code: 'PROFESSOR',
    color: '#4CAF50',
    icon: '👨‍🏫',
    description: 'Create content, assessments, and manage curriculum',
    agents: [
      {
        name: 'Assessor',
        icon: '📊',
        color: '#F44336',
        description: 'Create quizzes and assessments',
        keywords: ['quiz', 'test', 'assessment'],
      },
      {
        name: 'Content Creator',
        icon: '✨',
        color: '#FF9800',
        description: 'Generate educational content',
        keywords: ['create', 'content', 'lesson'],
      },
      {
        name: 'General Tutor',
        icon: '🎓',
        color: '#2196F3',
        description: 'Understand student needs',
        keywords: ['help', 'explain', 'teach'],
      },
      {
        name: 'Math Tutor',
        icon: '🔢',
        color: '#4CAF50',
        description: 'Math curriculum development',
        keywords: ['math', 'curriculum', 'problems'],
      },
      {
        name: 'Science Tutor',
        icon: '🔬',
        color: '#9C27B0',
        description: 'Science lesson planning',
        keywords: ['science', 'experiment', 'lab'],
      },
    ],
    sampleMessages: [
      'Create a calculus quiz for intermediate students',
      'Generate lesson content on cellular biology',
      'Help me design an assessment for algebra',
      'Create practice problems for physics',
      'Develop a writing assignment rubric',
    ],
  },
  {
    name: 'Admin',
    code: 'ADMIN',
    color: '#F44336',
    icon: '👑',
    description: 'System analytics, institutional planning, and oversight',
    agents: [
      {
        name: 'Assessor',
        icon: '📊',
        color: '#F44336',
        description: 'System-wide assessment analytics',
        keywords: ['analytics', 'assessment', 'data'],
      },
      {
        name: 'Content Creator',
        icon: '✨',
        color: '#FF9800',
        description: 'Institutional content creation',
        keywords: ['institutional', 'policy', 'content'],
      },
      {
        name: 'Advisor',
        icon: '🎯',
        color: '#9C27B0',
        description: 'Institutional guidance and planning',
        keywords: ['planning', 'strategy', 'guidance'],
      },
      {
        name: 'General Tutor',
        icon: '🎓',
        color: '#2196F3',
        description: 'System oversight and monitoring',
        keywords: ['oversight', 'monitoring', 'system'],
      },
      {
        name: 'Math Tutor',
        icon: '🔢',
        color: '#4CAF50',
        description: 'Math program oversight',
        keywords: ['program', 'math', 'oversight'],
      },
    ],
    sampleMessages: [
      'Analyze system-wide learning patterns',
      'Create institutional assessment strategy',
      'Generate administrative reports',
      'Plan curriculum improvements',
      'Monitor student progress trends',
    ],
  },
];

const RoleBasedMultiAgentChat: React.FC = () => {
  const { user } = useSelector((state: RootState) => state.auth);
  const navigate = useNavigate();
  const theme = useTheme();
  
  // Get user's role configuration based on their actual role
  const getUserRoleConfig = (): RoleConfig => {
    const userRole = user?.role?.toUpperCase() || 'STUDENT';
    return roleConfigs.find(role => role.code === userRole) || roleConfigs[0];
  };

  const [currentRole] = useState<RoleConfig>(getUserRoleConfig());
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [activeAgent, setActiveAgent] = useState<string | null>(null);
  const [conversations, setConversations] = useState<MultiAgentConversation[]>([]);
  const [activeConversation, setActiveConversation] = useState<MultiAgentConversation | null>(null);
  const [loadingConversations, setLoadingConversations] = useState(false);
  const messageEndRef = useRef<HTMLDivElement>(null);

  // Load conversations on component mount
  useEffect(() => {
    loadConversations();
  }, []);

  // Convert ChatMessage to MultiAgentMessage format
  const convertToMultiAgentMessage = (msg: ChatMessage): MultiAgentMessage => ({
    id: msg.id,
    content: msg.content,
    isUser: msg.isUser,
    agentUsed: msg.agentUsed,
    timestamp: msg.timestamp,
    metadata: msg.metadata,
  });

  const loadConversations = async () => {
    setLoadingConversations(true);
    try {
      const loadedConversations = await multiAgentConversationService.getConversations();
      setConversations(loadedConversations);
    } catch (error) {
      console.error('Error loading conversations:', error);
    } finally {
      setLoadingConversations(false);
    }
  };

  const createNewConversation = async () => {
    try {
      const newConv = await multiAgentConversationService.createConversation({
        title: 'New Multi-Agent Chat',
        userRole: currentRole.code,
      });
      setConversations(prev => [newConv, ...prev]);
      setActiveConversation(newConv);
      setMessages([]);
    } catch (error) {
      console.error('Error creating conversation:', error);
    }
  };

  const selectConversation = async (conversation: MultiAgentConversation) => {
    setActiveConversation(conversation);
    setMessages(conversation.messages.map(msg => ({
      id: msg.id,
      content: msg.content,
      isUser: msg.isUser,
      agentUsed: msg.agentUsed,
      timestamp: msg.timestamp,
      metadata: msg.metadata,
    })));
  };

  const deleteConversation = async (conversationId: string) => {
    try {
      await multiAgentConversationService.deleteConversation(conversationId);
      setConversations(prev => prev.filter(conv => conv.id !== conversationId));
      if (activeConversation?.id === conversationId) {
        setActiveConversation(null);
        setMessages([]);
      }
    } catch (error) {
      console.error('Error deleting conversation:', error);
    }
  };

  // Update conversation when messages change
  useEffect(() => {
    if (activeConversation && messages.length > 0) {
      const updateConversation = async () => {
        try {
          await multiAgentConversationService.updateConversation(
            activeConversation.id,
            messages.map(convertToMultiAgentMessage)
          );
          // Update the conversation in the list
          setConversations(prev => prev.map(conv => 
            conv.id === activeConversation.id 
              ? { ...conv, messages: messages.map(convertToMultiAgentMessage), updatedAt: new Date() }
              : conv
          ));
        } catch (error) {
          console.error('Error updating conversation:', error);
        }
      };
      updateConversation();
    }
  }, [messages, activeConversation]);

  const scrollToBottom = useCallback(() => {
    if (messageEndRef.current) {
      messageEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  const predictAgent = (message: string, role: RoleConfig): AgentInfo => {
    const lowerMessage = message.toLowerCase();

    // Check role-specific agent priorities
    for (const agent of role.agents) {
      if (agent.keywords.some(keyword => lowerMessage.includes(keyword))) {
        return agent;
      }
    }

    // Default to first agent for the role
    return role.agents[0];
  };

  const sendMessage = async () => {
    if (!inputMessage.trim()) return;

    // Create new conversation if none is active
    if (!activeConversation) {
      await createNewConversation();
    }

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: inputMessage,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    // Predict which agent will handle this based on user's role
    const predictedAgent = predictAgent(inputMessage, currentRole);
    setActiveAgent(predictedAgent.name);

    try {
      // Send message with user context using multi-agent service
      const response = await multiAgentService.testAgentResponse({
        agent_type: predictedAgent.name.toLowerCase().replace(' ', '_'),
        message: inputMessage,
        context: {
          user_role: user?.role,
          user_name: user?.first_name || user?.username,
          user_id: user?.id,
        },
      });

      console.log('Multi-agent response:', response); // Debug log

      // Handle different response structures
      let content = '';
      if (response?.data?.response?.content) {
        content = response.data.response.content;
      } else if (response?.data?.message) {
        content = response.data.message;
      } else if ((response as any)?.message) {
        content = (response as any).message;
      } else {
        content = `${currentRole.icon} ${predictedAgent.name}: I received your message, ${user?.first_name || user?.username || 'there'}! I'm here to help with ${predictedAgent.description.toLowerCase()}.`;
      }

      const botMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content,
        isUser: false,
        agentUsed:
          response?.data?.agent_type ||
          response?.data?.agent_name ||
          predictedAgent.name.toLowerCase().replace(' ', '_'),
        timestamp: new Date(),
        metadata: response?.data?.response?.metadata || (response as any)?.metadata,
      };

      setMessages(prev => [...prev, botMessage]);
    } catch (error: any) {
      console.error('Multi-agent chat error:', error); // Debug log
      
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: `${currentRole.icon} Hello ${user?.first_name || user?.username || 'there'}! As a ${currentRole.name}, this would be handled by ${predictedAgent.name} (${predictedAgent.icon}). The AI system recognizes your role and will provide ${currentRole.name.toLowerCase()}-specific assistance.`,
        isUser: false,
        agentUsed: predictedAgent.name.toLowerCase().replace(' ', '_'),
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    }

    setInputMessage('');
    setIsLoading(false);
    setActiveAgent(null);
  };

  const getAgentInfo = (agentUsed: string): AgentInfo => {
    for (const agent of currentRole.agents) {
      if (agentUsed?.includes(agent.name.toLowerCase().replace(' ', '_'))) {
        return agent;
      }
    }
    return currentRole.agents[0];
  };

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column', width: '100%' }}>
      <ChatModeTabNavigation />
      <Box 
        sx={{ 
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          position: 'relative',
          overflow: 'hidden',
          width: '100%',
          minHeight: 0, // Important for proper flex behavior
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'radial-gradient(circle at 20% 50%, rgba(102, 126, 234, 0.03) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(118, 75, 162, 0.03) 0%, transparent 50%)',
            pointerEvents: 'none',
          },
        }}
      >
      {/* Main Content */}
      <Box sx={{ flex: 1, display: 'flex', overflow: 'hidden', width: '100%', minHeight: 0 }}>
        {/* Left Sidebar - Conversations */}
        <SidebarPaper>
          {/* Sidebar Header */}
          <Box
            sx={{
              p: 2,
              background: 'linear-gradient(90deg, rgba(126, 59, 161, 0.2), rgba(126, 59, 161, 0.8), rgba(126, 59, 161, 0.2))',
              color: 'white',
              display: 'flex',
              alignItems: 'center',
              gap: 2,
              boxShadow: '0 2px 10px rgba(126, 59, 161, 0.2)',
              flexShrink: 0,
            }}
          >
            <Box
              sx={{
                width: 32,
                height: 32,
                borderRadius: '50%',
                background: 'rgba(255, 255, 255, 0.2)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backdropFilter: 'blur(10px)',
              }}
            >
              <HistoryIcon sx={{ fontSize: 16, color: 'white' }} />
            </Box>
            <Box sx={{ flex: 1 }}>
              <Typography variant='subtitle1' fontWeight={600} sx={{ lineHeight: 1.2 }}>
                Previous Conversations
              </Typography>
              <Typography variant='caption' sx={{ opacity: 0.8, fontSize: '0.7rem' }}>
                {currentRole.name} Chat History
              </Typography>
            </Box>
          </Box>

          {/* New Conversation Button */}
          <Box sx={{ p: 1, borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}` }}>
            <CreateButton
              fullWidth
              startIcon={<AddIcon />}
              onClick={createNewConversation}
              size="small"
            >
              New Chat
            </CreateButton>
          </Box>

          {/* Conversations List */}
          <Box sx={{ flex: 1, overflow: 'auto' }}>
            {loadingConversations ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress size={24} />
              </Box>
            ) : conversations.length === 0 ? (
              <Box sx={{ p: 3, textAlign: 'center' }}>
                <Typography variant="body2" color="text.secondary">
                  No conversations yet. Start a new chat!
                </Typography>
              </Box>
            ) : (
              <List sx={{ p: 0 }}>
                {conversations.map((conversation) => (
                  <StyledListItem
                    key={conversation.id}
                    selected={activeConversation?.id === conversation.id}
                    onClick={() => selectConversation(conversation)}
                  >
                    <ListItemText
                      primary={
                        <Typography variant="body2" noWrap sx={{ fontWeight: 500 }}>
                          {conversation.title}
                        </Typography>
                      }
                      secondary={
                        <Typography variant="caption" color="text.secondary">
                          {conversation.updatedAt.toLocaleDateString()}
                        </Typography>
                      }
                    />
                    <Tooltip title="Delete conversation">
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          deleteConversation(conversation.id);
                        }}
                        sx={{
                          opacity: 0.6,
                          '&:hover': { opacity: 1, color: 'error.main' },
                        }}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </StyledListItem>
                ))}
              </List>
            )}
          </Box>
        </SidebarPaper>

        {/* Right Side - Chat Area */}
        <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', minHeight: 0, overflow: 'hidden', ml: 2 }}>
          {/* Chat Header */}
          <Box
            sx={{
              p: 2,
              background: 'linear-gradient(90deg, rgba(126, 59, 161, 0.2), rgba(126, 59, 161, 0.8), rgba(126, 59, 161, 0.2))',
              color: 'white',
              display: 'flex',
              alignItems: 'center',
              gap: 2,
              boxShadow: '0 2px 10px rgba(126, 59, 161, 0.2)',
              flexShrink: 0,
              borderRadius: `${theme.spacing(2)} ${theme.spacing(2)} 0 0`,
            }}
          >
            <Box
              sx={{
                width: 40,
                height: 40,
                borderRadius: '50%',
                background: 'rgba(255, 255, 255, 0.2)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backdropFilter: 'blur(10px)',
              }}
            >
              <AutoAwesome sx={{ fontSize: 20, color: 'white' }} />
            </Box>
            <Box sx={{ flex: 1 }}>
              <Typography variant='h6' fontWeight={600} sx={{ lineHeight: 1.2 }}>
                Multi-Agent Chat - {currentRole.name}
              </Typography>
              <Typography variant='caption' sx={{ opacity: 0.8, fontSize: '0.75rem' }}>
                Specialized AI experts for {user?.first_name || user?.username || 'User'}
              </Typography>
            </Box>
            
            {/* User Info Display */}
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 2,
                background: 'rgba(255, 255, 255, 0.15)',
                px: 3,
                py: 1,
                borderRadius: 20,
                backdropFilter: 'blur(10px)',
              }}
            >
              <Box sx={{ textAlign: 'right' }}>
                <Typography variant='body2' sx={{ fontSize: '0.9rem', fontWeight: 600 }}>
                  {user?.first_name || user?.username || 'User'}
                </Typography>
                <Typography variant='caption' sx={{ fontSize: '0.75rem', opacity: 0.8 }}>
                  {currentRole.name}
                </Typography>
              </Box>
              <Box
                sx={{
                  width: 32,
                  height: 32,
                  borderRadius: '50%',
                  background: `linear-gradient(135deg, ${currentRole.color} 0%, ${alpha(currentRole.color, 0.7)} 100%)`,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontWeight: 600,
                  fontSize: '0.9rem',
                }}
              >
                {(user?.first_name || user?.username || 'U').charAt(0).toUpperCase()}
              </Box>
            </Box>
            
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                background: 'rgba(255, 255, 255, 0.15)',
                px: 2,
                py: 0.5,
                borderRadius: 20,
                backdropFilter: 'blur(10px)',
              }}
            >
              <Box
                sx={{
                  width: 8,
                  height: 8,
                  borderRadius: '50%',
                  backgroundColor: '#4ade80',
                  animation: 'pulse 2s infinite',
                  '@keyframes pulse': {
                    '0%, 100%': { opacity: 1 },
                    '50%': { opacity: 0.5 },
                  },
                }}
              />
              <Typography variant='body2' sx={{ fontSize: '0.8rem' }}>
                Online
              </Typography>
            </Box>
          </Box>
          {/* Messages container */}
          <Box
            sx={{
              flex: 1,
              overflow: 'auto',
              p: 2,
              display: 'flex',
              flexDirection: 'column',
              gap: 1,
              position: 'relative',
              zIndex: 1,
              minHeight: 0,
              background: theme.palette.mode === 'dark'
                ? 'rgba(30, 30, 30, 0.95)'
                : 'rgba(248, 250, 252, 0.95)',
              '&::-webkit-scrollbar': {
                width: 4,
              },
              '&::-webkit-scrollbar-track': {
                background: 'transparent',
              },
              '&::-webkit-scrollbar-thumb': {
                background: 'rgba(102, 126, 234, 0.3)',
                borderRadius: 2,
                '&:hover': {
                  background: 'rgba(102, 126, 234, 0.5)',
                },
              },
            }}
          >
            {messages.length === 0 && (
              <Box 
                sx={{ 
                  textAlign: 'center', 
                  py: 6,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: '100%',
                }}
              >
                <Box
                  sx={{
                    width: 70,
                    height: 70,
                    borderRadius: '50%',
                    background: `linear-gradient(135deg, ${alpha(currentRole.color, 0.1)} 0%, ${alpha(currentRole.color, 0.2)} 100%)`,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mb: 2.5,
                    animation: 'float 3s ease-in-out infinite',
                    '@keyframes float': {
                      '0%, 100%': { transform: 'translateY(0px)' },
                      '50%': { transform: 'translateY(-10px)' },
                    },
                  }}
                >
                  <span style={{ fontSize: '36px' }}>{currentRole.icon}</span>
                </Box>
                <Typography variant='h6' color='text.primary' sx={{ mb: 1, fontWeight: 600 }}>
                  Welcome to {currentRole.name} Multi-Agent Chat
                </Typography>
                <Typography variant='body2' color='text.secondary' sx={{ mb: 3, maxWidth: 350, lineHeight: 1.5 }}>
                  {currentRole.description}. Choose from the sample messages or start typing to see which agent responds!
                </Typography>
              </Box>
            )}

            {messages.map(message => (
              <Box
                key={message.id}
                sx={{
                  display: 'flex',
                  justifyContent: message.isUser ? 'flex-end' : 'flex-start',
                  mb: 1,
                }}
              >
                <Paper
                  sx={{
                    p: 2.5,
                    maxWidth: '70%',
                    borderRadius: message.isUser ? '20px 20px 4px 20px' : '20px 20px 20px 4px',
                    background: message.isUser 
                      ? `linear-gradient(135deg, ${currentRole.color} 0%, ${alpha(currentRole.color, 0.8)} 100%)`
                      : (theme) => theme.palette.mode === 'dark' 
                        ? 'rgba(40, 40, 45, 0.95)' 
                        : 'rgba(255, 255, 255, 0.95)',
                    color: message.isUser ? 'white' : 'text.primary',
                    boxShadow: message.isUser 
                      ? `0 4px 16px ${alpha(currentRole.color, 0.25)}`
                      : (theme) => theme.palette.mode === 'dark'
                        ? '0 2px 12px rgba(0, 0, 0, 0.3)'
                        : '0 2px 12px rgba(0, 0, 0, 0.08)',
                    border: message.isUser ? 'none' : (theme) => theme.palette.mode === 'dark' 
                      ? '1px solid rgba(255, 255, 255, 0.1)'
                      : `1px solid ${theme.palette.grey[200]}`,
                    position: 'relative',
                    transition: 'all 0.2s ease',
                    '&:hover': {
                      transform: message.isUser ? 'translateY(-1px)' : 'translateX(2px)',
                      boxShadow: message.isUser 
                        ? `0 6px 20px ${alpha(currentRole.color, 0.35)}`
                        : (theme) => theme.palette.mode === 'dark'
                          ? '0 4px 16px rgba(0, 0, 0, 0.4)'
                          : '0 4px 16px rgba(0, 0, 0, 0.12)',
                    },
                  }}
                >
                  {!message.isUser && message.agentUsed && (
                    <Box sx={{ mb: 1 }}>
                      <Chip
                        icon={
                          <span>
                            {getAgentInfo(message.agentUsed).icon}
                          </span>
                        }
                        label={getAgentInfo(message.agentUsed).name}
                        size='small'
                        sx={{
                          backgroundColor: (theme) => alpha(theme.palette.primary.main, 0.1),
                          color: 'primary.main',
                          border: (theme) => `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                          fontWeight: 500,
                        }}
                      />
                    </Box>
                  )}
                  <Typography variant='body2' sx={{ lineHeight: 1.6 }}>
                    {message.content}
                  </Typography>
                  <Typography
                    variant='caption'
                    display='block'
                    sx={{ 
                      mt: 1, 
                      opacity: 0.7,
                      fontSize: '0.7rem',
                    }}
                  >
                    {message.timestamp.toLocaleTimeString()}
                  </Typography>
                </Paper>
              </Box>
            ))}

            {isLoading && (
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'flex-start',
                  mb: 2,
                }}
              >
                <Paper
                  sx={{
                    p: 2,
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    borderRadius: '20px 20px 20px 4px',
                    background: (theme) => theme.palette.mode === 'dark' 
                      ? 'rgba(40, 40, 45, 0.95)' 
                      : 'rgba(255, 255, 255, 0.95)',
                    boxShadow: (theme) => theme.palette.mode === 'dark'
                      ? '0 2px 12px rgba(0, 0, 0, 0.3)'
                      : '0 2px 12px rgba(0, 0, 0, 0.08)',
                  }}
                >
                  <CircularProgress size={16} />
                  <Typography variant='body2'>
                    {activeAgent} is thinking...
                  </Typography>
                </Paper>
              </Box>
            )}
            <div ref={messageEndRef} />
          </Box>

          {/* Input form */}
          <Box
            sx={{
              p: 2,
              borderTop: '1px solid',
              borderColor: (theme) => alpha(theme.palette.primary.main, 0.1),
              background: (theme) => theme.palette.mode === 'dark'
                ? 'rgba(30, 30, 30, 0.98)'
                : 'rgba(248, 250, 252, 0.98)',
              backdropFilter: 'blur(20px)',
              flexShrink: 0,
              borderRadius: `0 0 ${theme.spacing(2)} ${theme.spacing(2)}`,
            }}
          >
            <Paper
              component='form'
              onSubmit={(e) => {
                e.preventDefault();
                sendMessage();
              }}
              elevation={0}
              sx={{
                p: 1.5,
                display: 'flex',
                alignItems: 'flex-end',
                gap: 1.5,
                borderRadius: 20,
                background: (theme) => theme.palette.mode === 'dark' 
                  ? 'rgba(40, 40, 45, 0.95)'
                  : 'rgba(255, 255, 255, 0.95)',
                backdropFilter: 'blur(10px)',
                border: '1px solid',
                borderColor: (theme) => alpha(theme.palette.primary.main, 0.1),
                boxShadow: (theme) => theme.palette.mode === 'dark' 
                  ? '0 2px 12px rgba(0, 0, 0, 0.2)' 
                  : '0 2px 12px rgba(0, 0, 0, 0.05)',
                '&:focus-within': {
                  borderColor: 'primary.main',
                  boxShadow: (theme) => theme.palette.mode === 'dark'
                    ? '0 4px 16px rgba(102, 126, 234, 0.2)'
                    : '0 4px 16px rgba(102, 126, 234, 0.1)',
                },
                transition: 'all 0.2s ease',
              }}
            >
              <TextField
                fullWidth
                value={inputMessage}
                onChange={e => setInputMessage(e.target.value)}
                placeholder={`Type a message as ${currentRole.name.toLowerCase()}...`}
                disabled={isLoading}
                multiline
                maxRows={4}
                variant="standard"
                InputProps={{
                  disableUnderline: true,
                  sx: { 
                    fontSize: '1rem',
                    color: 'text.primary',
                    '& .MuiInputBase-input': {
                      py: 1,
                      color: 'text.primary',
                      '&::placeholder': {
                        color: 'text.secondary',
                        opacity: 0.7,
                      },
                      '&:disabled': {
                        color: 'text.disabled',
                      },
                    },
                  },
                }}
                onKeyDown={e => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                  }
                }}
              />
              <IconButton
                type='submit'
                disabled={!inputMessage.trim() || isLoading}
                sx={{
                  background: `linear-gradient(135deg, ${currentRole.color} 0%, ${alpha(currentRole.color, 0.8)} 100%)`,
                  color: 'white',
                  width: 40,
                  height: 40,
                  boxShadow: `0 2px 8px ${alpha(currentRole.color, 0.25)}`,
                  '&:hover': {
                    background: `linear-gradient(135deg, ${alpha(currentRole.color, 0.9)} 0%, ${alpha(currentRole.color, 0.7)} 100%)`,
                    transform: 'scale(1.05)',
                    boxShadow: `0 4px 12px ${alpha(currentRole.color, 0.35)}`,
                  },
                  '&:disabled': {
                    background: (theme) => theme.palette.mode === 'dark'
                      ? 'rgba(255, 255, 255, 0.12)'
                      : '#e2e8f0',
                    color: (theme) => theme.palette.mode === 'dark'
                      ? 'rgba(255, 255, 255, 0.3)'
                      : '#94a3b8',
                    transform: 'none',
                    boxShadow: 'none',
                  },
                  transition: 'all 0.2s ease',
                }}
              >
                {isLoading ? (
                  <Box
                    sx={{
                      width: 16,
                      height: 16,
                      border: '2px solid currentColor',
                      borderTop: '2px solid transparent',
                      borderRadius: '50%',
                      animation: 'spin 1s linear infinite',
                      '@keyframes spin': {
                        '0%': { transform: 'rotate(0deg)' },
                        '100%': { transform: 'rotate(360deg)' },
                      },
                    }}
                  />
                ) : (
                  <FiSend size={16} />
                )}
              </IconButton>
            </Paper>

            {/* Prediction */}
            {inputMessage && (
              <Box sx={{ mt: 2, textAlign: 'center' }}>
                <Typography variant='caption' color='text.secondary'>
                  Predicted agent: {predictAgent(inputMessage, currentRole).icon}{' '}
                  {predictAgent(inputMessage, currentRole).name}
                </Typography>
              </Box>
            )}
          </Box>
        </Box>
      </Box>
    </Box>
    </Box>
  );
};

export default RoleBasedMultiAgentChat;
