/**
 * CourseCard Component
 * 
 * A comprehensive course card component with enrollment,
 * favorites, progress tracking, and accessibility features.
 */

import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardMedia,
  CardActions,
  Typography,
  Button,
  Chip,
  Box,
  Rating,
  LinearProgress,
  IconButton,
  Tooltip,
  Avatar,
  Divider,
} from '@mui/material';
import {
  Favorite,
  FavoriteBorder,
  Person,
  Schedule,
  Star,
  PlayArrow,
  BookmarkBorder,
  Bookmark,
} from '@mui/icons-material';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { RootState } from '../../store';

interface Instructor {
  id: number;
  name: string;
  email: string;
  avatar?: string;
}

interface Course {
  id: number;
  title: string;
  description: string;
  code: string;
  credits: number;
  level: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED';
  instructor: Instructor;
  enrollment_count: number;
  max_students: number;
  duration: string;
  rating: number;
  thumbnail?: string;
  tags: string[];
  price: number;
  is_enrolled: boolean;
  is_favorite: boolean;
  progress: number;
}

interface CourseCardProps {
  course: Course;
  onEnroll?: (courseId: number) => void;
  onToggleFavorite?: (courseId: number) => void;
  onViewDetails?: (courseId: number) => void;
  variant?: 'default' | 'compact' | 'detailed';
  showProgress?: boolean;
  showEnrollButton?: boolean;
}

const CourseCard: React.FC<CourseCardProps> = ({
  course,
  onEnroll,
  onToggleFavorite,
  onViewDetails,
  variant = 'default',
  showProgress = true,
  showEnrollButton = true,
}) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { user } = useSelector((state: RootState) => state.auth);

  const [isLoading, setIsLoading] = useState(false);
  const [imageError, setImageError] = useState(false);

  const handleEnroll = async () => {
    if (!user) {
      navigate('/login');
      return;
    }

    setIsLoading(true);
    try {
      if (onEnroll) {
        await onEnroll(course.id);
      }
    } catch (error) {
      console.error('Enrollment failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleFavorite = async () => {
    if (!user) {
      navigate('/login');
      return;
    }

    try {
      if (onToggleFavorite) {
        await onToggleFavorite(course.id);
      }
    } catch (error) {
      console.error('Toggle favorite failed:', error);
    }
  };

  const handleViewDetails = () => {
    if (onViewDetails) {
      onViewDetails(course.id);
    } else {
      navigate(`/courses/${course.id}`);
    }
  };

  const handleContinueLearning = () => {
    navigate(`/courses/${course.id}/learn`);
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'BEGINNER':
        return 'success';
      case 'INTERMEDIATE':
        return 'warning';
      case 'ADVANCED':
        return 'error';
      default:
        return 'default';
    }
  };

  const formatPrice = (price: number) => {
    return price === 0 ? 'Free' : `$${price.toFixed(2)}`;
  };

  const enrollmentPercentage = (course.enrollment_count / course.max_students) * 100;

  return (
    <Card
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: 4,
        },
      }}
    >
      {/* Course Image */}
      <CardMedia
        component="img"
        height={variant === 'compact' ? 120 : 200}
        image={imageError ? '/images/default-course.jpg' : course.thumbnail}
        alt={course.title}
        onError={() => setImageError(true)}
        sx={{
          objectFit: 'cover',
          cursor: 'pointer',
        }}
        onClick={handleViewDetails}
      />

      <CardContent sx={{ flexGrow: 1, pb: 1 }}>
        {/* Course Header */}
        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
          <Typography
            variant="h6"
            component="h3"
            sx={{
              fontWeight: 600,
              cursor: 'pointer',
              '&:hover': { color: 'primary.main' },
            }}
            onClick={handleViewDetails}
          >
            {course.title}
          </Typography>
          
          <Tooltip title={course.is_favorite ? 'Remove from favorites' : 'Add to favorites'}>
            <IconButton
              size="small"
              onClick={handleToggleFavorite}
              color={course.is_favorite ? 'error' : 'default'}
            >
              {course.is_favorite ? <Favorite /> : <FavoriteBorder />}
            </IconButton>
          </Tooltip>
        </Box>

        {/* Course Code and Level */}
        <Box display="flex" gap={1} mb={1}>
          <Chip
            label={course.code}
            size="small"
            variant="outlined"
            color="primary"
          />
          <Chip
            label={course.level}
            size="small"
            color={getLevelColor(course.level) as any}
          />
        </Box>

        {/* Description */}
        <Typography
          variant="body2"
          color="text.secondary"
          sx={{
            mb: 2,
            display: '-webkit-box',
            WebkitLineClamp: variant === 'compact' ? 2 : 3,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden',
          }}
        >
          {course.description}
        </Typography>

        {/* Instructor */}
        <Box display="flex" alignItems="center" gap={1} mb={1}>
          <Avatar
            src={course.instructor.avatar}
            sx={{ width: 24, height: 24 }}
          >
            <Person />
          </Avatar>
          <Typography variant="body2" color="text.secondary">
            {course.instructor.name}
          </Typography>
        </Box>

        {/* Rating and Duration */}
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
          <Box display="flex" alignItems="center" gap={0.5}>
            <Rating value={course.rating} precision={0.1} size="small" readOnly />
            <Typography variant="body2" color="text.secondary">
              ({course.rating})
            </Typography>
          </Box>
          
          <Box display="flex" alignItems="center" gap={0.5}>
            <Schedule fontSize="small" color="action" />
            <Typography variant="body2" color="text.secondary">
              {course.duration}
            </Typography>
          </Box>
        </Box>

        {/* Progress Bar (if enrolled) */}
        {course.is_enrolled && showProgress && (
          <Box mb={2}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={0.5}>
              <Typography variant="body2" color="text.secondary">
                Progress
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {course.progress}%
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={course.progress}
              sx={{ height: 6, borderRadius: 3 }}
            />
          </Box>
        )}

        {/* Tags */}
        <Box display="flex" flexWrap="wrap" gap={0.5} mb={1}>
          {course.tags.slice(0, 3).map((tag, index) => (
            <Chip
              key={index}
              label={tag}
              size="small"
              variant="outlined"
              sx={{ fontSize: '0.7rem' }}
            />
          ))}
          {course.tags.length > 3 && (
            <Chip
              label={`+${course.tags.length - 3}`}
              size="small"
              variant="outlined"
              sx={{ fontSize: '0.7rem' }}
            />
          )}
        </Box>

        <Divider sx={{ my: 1 }} />

        {/* Enrollment Info */}
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="body2" color="text.secondary">
            {course.enrollment_count}/{course.max_students} enrolled
          </Typography>
          <Typography variant="h6" color="primary.main" fontWeight="bold">
            {formatPrice(course.price)}
          </Typography>
        </Box>
      </CardContent>

      {/* Actions */}
      <CardActions sx={{ p: 2, pt: 0 }}>
        {course.is_enrolled ? (
          <Button
            fullWidth
            variant="contained"
            startIcon={<PlayArrow />}
            onClick={handleContinueLearning}
          >
            Continue Learning
          </Button>
        ) : (
          showEnrollButton && (
            <Button
              fullWidth
              variant="contained"
              onClick={handleEnroll}
              disabled={isLoading || enrollmentPercentage >= 100}
            >
              {isLoading ? 'Enrolling...' : 'Enroll Now'}
            </Button>
          )
        )}
      </CardActions>
    </Card>
  );
};

export default CourseCard;
