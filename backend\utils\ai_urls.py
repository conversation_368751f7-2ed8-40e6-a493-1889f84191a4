"""URLConfigurationforImprovedAIServices"""from django.urlsimportpathfrom.importai_viewsapp_name='ai_improved'urlpatterns=[#Contentgenerationpath('generate/'ai_views.generate_contentname='generate_content')path('generate-async/'ai_views.generate_content_asyncname='generate_content_async')#Asyncrequestmanagementpath('async-status/<str:request_id>/'ai_views.async_statusname='async_status')path('async-result/<str:request_id>/'ai_views.async_resultname='async_result')#Assessmentpath('analyze-answer/'ai_views.analyze_answername='analyze_answer')#Monitoringandhealthpath('health/'ai_views.health_statusname='health_status')path('analytics/'ai_views.usage_analyticsname='usage_analytics')path('metrics/'ai_views.service_metricsname='service_metrics')#Configuration(adminonly)path('config/'ai_views.update_configurationname='update_configuration')path('api-key/'ai_views.update_api_keyname='update_api_key')]