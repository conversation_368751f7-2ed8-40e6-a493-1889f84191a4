"""
URL patterns for the utils app. 
This module defines the URL patterns for the utils app including 
analytics, progress tracking endpoints and monitoring dashboard.
"""

from django.urls import include, path
from rest_framework.routers import DefaultRouter

# Import views
from . import enhanced_ai_views, ai_config_views, ai_views, ai_dashboard_views
from .monitoring_views import MonitoringMetricsView, ResetMonitoringMetricsView
from .progress_views import ProgressViewSet

# Import URL patterns
from .ai_config_urls import urlpatterns as ai_config_urlpatterns
from .syrian_dialect_urls import urlpatterns as syrian_dialect_urlpatterns
from .ai_urls import urlpatterns as improved_ai_urlpatterns

# Create a router for the progress tracking API
router = DefaultRouter()
router.register(r"progress", ProgressViewSet, basename="progress")

urlpatterns = [
    path("", include(router.urls)),
    
    # Analytics endpoints
    path("analytics/", include("utils.analytics_urls")),
    
    # Unified AI Configuration endpoints
    path("ai/config/", include(ai_config_urlpatterns)),
    
    # Syrian Dialect endpoints
    path("syrian-dialect/", include(syrian_dialect_urlpatterns)),
    
    # Improved AI Service endpoints
    path("ai/improved/", include(improved_ai_urlpatterns)),
    
    # AI Dashboard endpoints
    path("ai/dashboard/overview/", ai_dashboard_views.dashboard_overview, name="ai_dashboard_overview"),
    path("ai/dashboard/performance/", ai_dashboard_views.performance_metrics, name="ai_dashboard_performance"),
    path("ai/dashboard/cost/", ai_dashboard_views.cost_analytics, name="ai_dashboard_cost"),
    
    # Direct AI endpoints (for frontend API calls)
    # Chat endpoints - order matters! More specific paths first
    path("ai/chat/conversations/", ai_config_views.conversations_endpoint, name="ai_chat_conversations"),
    path("ai/chat/conversations/<int:conversation_id>/", ai_config_views.conversation_detail_endpoint, name="ai_chat_conversation_detail"),
    path("ai/chat/", ai_config_views.chat_endpoint, name="ai_chat"),
    path("ai/multi-agent-chat/", ai_config_views.multi_agent_chat_endpoint, name="ai_multi_agent_chat"),
    path("ai/assistant/", ai_config_views.assistant_endpoint, name="ai_assistant"),
    path("ai/tutor/", ai_config_views.tutor_endpoint, name="ai_tutor"),
    path("ai/health/", ai_config_views.ai_health_check, name="ai_health_check"),
    
    # Monitoring endpoints
    path("monitoring/metrics/", MonitoringMetricsView.as_view(), name="monitoring-metrics"),
    path("monitoring/reset/", ResetMonitoringMetricsView.as_view(), name="reset-monitoring-metrics"),
    
    # Enhanced AI endpoints
    # LangGraph Workflows
    path("ai/workflows/content-generation/", enhanced_ai_views.execute_content_generation_workflow, name="content_generation_workflow"),
    path("ai/workflows/learning-path/", enhanced_ai_views.execute_learning_path_workflow, name="learning_path_workflow"),
    path("ai/workflows/assessment/", enhanced_ai_views.execute_assessment_workflow, name="assessment_workflow"),
    
    # ML Analytics
    path("ai/analytics/learning-patterns/", enhanced_ai_views.analyze_learning_patterns, name="analyze_learning_patterns"),
    path("ai/recommendations/content/", enhanced_ai_views.get_content_recommendations, name="content_recommendations"),
    
    # AI Agents
    path("ai/agents/interact/", enhanced_ai_views.interact_with_ai_agent, name="ai_agent_interaction"),
    
    # Intelligent Tutoring System
    path("ai/tutoring/start-session/", enhanced_ai_views.start_tutoring_session, name="start_tutoring_session"),
    path("ai/tutoring/interact/", enhanced_ai_views.process_tutoring_interaction, name="tutoring_interaction"),
    path("ai/tutoring/end-session/", enhanced_ai_views.end_tutoring_session, name="end_tutoring_session"),
    
    # System Status
    path("ai/status/", enhanced_ai_views.get_ai_system_status, name="ai_system_status"),
]
