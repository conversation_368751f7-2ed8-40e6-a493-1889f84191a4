from django.urlsimportincludepathfromrest_framework.routersimportDefaultRouterfrom.viewsimport(ChatConversationViewSetChatMessageViewSetChatSessionViewSetChatViewPathAdvisorChatBotViewUserLearningProfileViewSet)router=DefaultRouter()router.register(r"conversations"ChatConversationViewSetbasename="chat-conversation")router.register(r"messages"ChatMessageViewSetbasename="chat-message")router.register(r"sessions"ChatSessionViewSetbasename="chat-session")router.register(r"learning-profile"UserLearningProfileViewSetbasename="learning-profile")urlpatterns=[path(""include(router.urls))path("chat/"ChatView.as_view()name="chat")path("path-advisor-chat/"PathAdvisorChatBotView.as_view()name="path-advisor-chat")]