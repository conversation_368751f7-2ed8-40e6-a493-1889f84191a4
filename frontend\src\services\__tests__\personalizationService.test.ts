/**
 * Tests for PersonalizationService
 */

import { personalizationService } from '../personalizationService';
import { 
  EmotionalLearningState, 
  VoiceLearningPreferences, 
  VisualLearningPreferences,
  MicroLearningPreferences,
  StudentProfile,
  LearningStyle,
  DifficultyLevel
} from '../personalizedLearningService';

// Mock the emotional intelligence service
jest.mock('../emotionalIntelligenceService', () => ({
  detectEmotionalState: jest.fn(),
  adaptContentToEmotion: jest.fn(),
}));

describe('PersonalizationService', () => {
  const mockStudentProfile: StudentProfile = {
    id: 1,
    user_id: 123,
    learning_style: LearningStyle.VISUAL,
    preferred_difficulty: DifficultyLevel.INTERMEDIATE,
    interests: [],
    knowledge_gaps: [],
    emotional_state: {
      current_mood: 'motivated',
      stress_level: 3,
      confidence_level: 7,
      engagement_level: 8,
      learning_readiness: 7,
      detected_at: new Date().toISOString()
    },
    voice_preferences: {
      preferred_speech_rate: 150,
      preferred_voice_type: 'neutral',
      accent_preference: 'us',
      audio_learning_style: 'conversational',
      voice_enabled: true,
      volume_preference: 0.8
    },
    visual_preferences: {
      color_scheme: 'cool',
      diagram_complexity: 'detailed',
      animation_preference: 'subtle',
      layout_preference: 'linear',
      font_size_multiplier: 1.2,
      contrast_level: 3
    },
    micro_learning_preferences: {
      optimal_chunk_duration: 15,
      break_frequency: 25,
      preferred_content_density: 'medium',
      chunking_strategy: 'concept_based'
    },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  beforeEach(() => {
    // Set up the user profile for testing
    personalizationService.setUserProfile('123', mockStudentProfile);
  });

  describe('personalizeContent', () => {
    it('should return original content when no profile exists', async () => {
      const content = 'This is test content';
      const context = {
        userId: '999', // Non-existent user
        deviceType: 'desktop' as const,
        timeOfDay: 14,
        availableTime: 30,
        networkQuality: 'high' as const
      };

      const result = await personalizationService.personalizeContent(
        content,
        '999',
        context
      );

      expect(result).toBe(content);
    });

    it('should apply micro-learning chunking for concept-based strategy', async () => {
      const content = 'First concept.\n\nSecond concept.\n\nThird concept.';
      const context = {
        userId: '123',
        deviceType: 'desktop' as const,
        timeOfDay: 14,
        availableTime: 30,
        networkQuality: 'high' as const
      };

      const result = await personalizationService.personalizeContent(
        content,
        '123',
        context
      );

      expect(result).toContain('📚 Concept 1:');
      expect(result).toContain('📚 Concept 2:');
      expect(result).toContain('📚 Concept 3:');
    });

    it('should apply visual adaptation markers', async () => {
      const content = 'Test content';
      const context = {
        userId: '123',
        deviceType: 'desktop' as const,
        timeOfDay: 14,
        availableTime: 30,
        networkQuality: 'high' as const
      };

      const result = await personalizationService.personalizeContent(
        content,
        '123',
        context
      );

      expect(result).toContain('[THEME:cool]');
      expect(result).toContain('[FONT_SIZE:1.2]');
      expect(result).toContain('[ANIMATION:subtle]');
    });

    it('should optimize for mobile devices', async () => {
      const content = 'Test content';
      const context = {
        userId: '123',
        deviceType: 'mobile' as const,
        timeOfDay: 14,
        availableTime: 30,
        networkQuality: 'high' as const
      };

      const result = await personalizationService.personalizeContent(
        content,
        '123',
        context
      );

      expect(result).toContain('📱 **Mobile Optimized**');
    });

    it('should create quick version for limited time', async () => {
      const content = 'First sentence. Second sentence. Third sentence. Fourth sentence.';
      const context = {
        userId: '123',
        deviceType: 'desktop' as const,
        timeOfDay: 14,
        availableTime: 5, // Limited time
        networkQuality: 'high' as const
      };

      const result = await personalizationService.personalizeContent(
        content,
        '123',
        context
      );

      expect(result).toContain('⚡ **Quick Summary:**');
      expect(result).toContain('*Want the full explanation? Let me know!*');
    });

    it('should optimize for low bandwidth', async () => {
      const content = '🌟 Test content with emojis 📚';
      const context = {
        userId: '123',
        deviceType: 'desktop' as const,
        timeOfDay: 14,
        availableTime: 30,
        networkQuality: 'low' as const
      };

      const result = await personalizationService.personalizeContent(
        content,
        '123',
        context
      );

      // Should remove emojis for low bandwidth
      expect(result).not.toContain('🌟');
      expect(result).not.toContain('📚');
    });
  });

  describe('getTTSOptions', () => {
    it('should return correct TTS options', () => {
      const voicePrefs: VoiceLearningPreferences = {
        preferred_speech_rate: 200,
        preferred_voice_type: 'female',
        accent_preference: 'uk',
        audio_learning_style: 'lecture',
        voice_enabled: true,
        volume_preference: 0.9
      };

      const options = personalizationService.getTTSOptions(voicePrefs);

      expect(options.rate).toBe(1); // 200/200 = 1
      expect(options.volume).toBe(0.9);
      expect(options.voice).toBe('female');
      expect(options.lang).toBe('en-GB');
    });

    it('should default to US English for non-UK accents', () => {
      const voicePrefs: VoiceLearningPreferences = {
        preferred_speech_rate: 150,
        preferred_voice_type: 'male',
        accent_preference: 'us',
        audio_learning_style: 'conversational',
        voice_enabled: true,
        volume_preference: 0.7
      };

      const options = personalizationService.getTTSOptions(voicePrefs);

      expect(options.lang).toBe('en-US');
    });
  });

  describe('applyVisualTheme', () => {
    let mockDocumentElement: any;

    beforeEach(() => {
      mockDocumentElement = {
        setAttribute: jest.fn(),
        style: {
          setProperty: jest.fn()
        }
      };
      
      Object.defineProperty(document, 'documentElement', {
        value: mockDocumentElement,
        writable: true
      });
    });

    it('should apply visual theme to document', () => {
      const visualPrefs: VisualLearningPreferences = {
        color_scheme: 'dark',
        diagram_complexity: 'simple',
        animation_preference: 'none',
        layout_preference: 'grid',
        font_size_multiplier: 1.5,
        contrast_level: 5
      };

      personalizationService.applyVisualTheme(visualPrefs);

      expect(mockDocumentElement.setAttribute).toHaveBeenCalledWith('data-color-scheme', 'dark');
      expect(mockDocumentElement.setAttribute).toHaveBeenCalledWith('data-animation-preference', 'none');
      expect(mockDocumentElement.setAttribute).toHaveBeenCalledWith('data-layout-preference', 'grid');
      expect(mockDocumentElement.style.setProperty).toHaveBeenCalledWith('--font-size-multiplier', '1.5');
    });

    it('should handle errors gracefully', () => {
      // Mock document.documentElement to throw an error
      Object.defineProperty(document, 'documentElement', {
        get: () => {
          throw new Error('Test error');
        }
      });

      const visualPrefs: VisualLearningPreferences = {
        color_scheme: 'light',
        diagram_complexity: 'detailed',
        animation_preference: 'subtle',
        layout_preference: 'linear',
        font_size_multiplier: 1.0,
        contrast_level: 3
      };

      // Should not throw an error
      expect(() => {
        personalizationService.applyVisualTheme(visualPrefs);
      }).not.toThrow();
    });
  });

  describe('speakText', () => {
    let mockSpeechSynthesis: any;
    let mockUtterance: any;

    beforeEach(() => {
      mockUtterance = {
        rate: 0,
        pitch: 0,
        volume: 0,
        lang: '',
        voice: null
      };

      mockSpeechSynthesis = {
        cancel: jest.fn(),
        speak: jest.fn(),
        getVoices: jest.fn(() => [
          { name: 'Female Voice', lang: 'en-US' },
          { name: 'Male Voice', lang: 'en-GB' }
        ])
      };

      // Mock SpeechSynthesisUtterance constructor
      global.SpeechSynthesisUtterance = jest.fn(() => mockUtterance);
      
      // Mock speechSynthesis
      Object.defineProperty(window, 'speechSynthesis', {
        value: mockSpeechSynthesis,
        writable: true
      });
    });

    it('should speak text with correct settings', async () => {
      const voicePrefs: VoiceLearningPreferences = {
        preferred_speech_rate: 160,
        preferred_voice_type: 'female',
        accent_preference: 'us',
        audio_learning_style: 'conversational',
        voice_enabled: true,
        volume_preference: 0.8
      };

      await personalizationService.speakText('Hello world', voicePrefs);

      expect(mockSpeechSynthesis.cancel).toHaveBeenCalled();
      expect(global.SpeechSynthesisUtterance).toHaveBeenCalledWith('Hello world');
      expect(mockUtterance.rate).toBe(0.8); // 160/200
      expect(mockUtterance.volume).toBe(0.8);
      expect(mockUtterance.lang).toBe('en-US');
      expect(mockSpeechSynthesis.speak).toHaveBeenCalledWith(mockUtterance);
    });

    it('should not speak when voice is disabled', async () => {
      const voicePrefs: VoiceLearningPreferences = {
        preferred_speech_rate: 150,
        preferred_voice_type: 'neutral',
        accent_preference: 'us',
        audio_learning_style: 'conversational',
        voice_enabled: false, // Disabled
        volume_preference: 0.8
      };

      await personalizationService.speakText('Hello world', voicePrefs);

      expect(mockSpeechSynthesis.speak).not.toHaveBeenCalled();
    });

    it('should handle speechSynthesis not being available', async () => {
      // Remove speechSynthesis from window
      Object.defineProperty(window, 'speechSynthesis', {
        value: undefined,
        writable: true
      });

      const voicePrefs: VoiceLearningPreferences = {
        preferred_speech_rate: 150,
        preferred_voice_type: 'neutral',
        accent_preference: 'us',
        audio_learning_style: 'conversational',
        voice_enabled: true,
        volume_preference: 0.8
      };

      // Should not throw an error
      await expect(
        personalizationService.speakText('Hello world', voicePrefs)
      ).resolves.toBeUndefined();
    });
  });

  describe('setUserProfile and getUserProfile', () => {
    it('should store and retrieve user profiles', () => {
      const testProfile: StudentProfile = {
        ...mockStudentProfile,
        user_id: 456
      };

      personalizationService.setUserProfile('456', testProfile);

      // Test that the profile was stored by trying to personalize content
      const content = 'Test content';
      const context = {
        userId: '456',
        deviceType: 'desktop' as const,
        timeOfDay: 14,
        availableTime: 30,
        networkQuality: 'high' as const
      };

      return personalizationService.personalizeContent(content, '456', context)
        .then(result => {
          // Should apply personalization since profile exists
          expect(result).not.toBe(content);
        });
    });
  });
});
