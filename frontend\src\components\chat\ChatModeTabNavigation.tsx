import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Tabs, Tab, Paper } from '@mui/material';
import { Chat, AutoAwesome } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { styled, alpha } from '@mui/material/styles';
import { RootState } from '../../app/store';

const TabContainer = styled(Paper)(({ theme }) => ({
  background: theme.palette.background.paper,
  border: 'none',
  borderRadius: 0,
  overflow: 'hidden',
  marginBottom: 0,
  boxShadow: 'none',
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const StyledTabs = styled(Tabs)(({ theme }) => ({
  minHeight: 44,
  '& .MuiTabs-indicator': {
    height: 2,
    borderRadius: '2px 2px 0 0',
    background: theme.palette.primary.main,
  },
  '& .MuiTab-root': {
    minHeight: 44,
    textTransform: 'none',
    fontWeight: 500,
    fontSize: '0.9rem',
    padding: theme.spacing(0.5, 2),
    color: theme.palette.text.secondary,
    transition: 'all 0.2s ease',
    '&:hover': {
      color: theme.palette.primary.main,
      backgroundColor: alpha(theme.palette.primary.main, 0.04),
    },
    '&.Mui-selected': {
      color: theme.palette.primary.main,
      fontWeight: 600,
    },
  },
}));

const ChatModeTabNavigation: React.FC = () => {
  const { t } = useTranslation('chat');
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useSelector((state: RootState) => state.auth);

  // Determine the current tab based on the URL path
  const getCurrentTab = () => {
    if (location.pathname.includes('/multi-agent-chat')) {
      return 'multi-agent';
    }
    return 'standard';
  };

  const handleTabChange = (_event: React.SyntheticEvent, newValue: string) => {
    const role = user?.role?.toLowerCase() || 'student';
    
    if (newValue === 'standard') {
      navigate(`/${role}/chat`);
    } else if (newValue === 'multi-agent') {
      navigate(`/${role}/multi-agent-chat`);
    }
  };
  return (
    <TabContainer elevation={0}>
      <StyledTabs
        value={getCurrentTab()}
        onChange={handleTabChange}
        variant="fullWidth"
      >
        <Tab
          value="standard"
          icon={<Chat sx={{ fontSize: 20 }} />}
          iconPosition="start"
          label={t('standardChat', 'Standard Chat')}
        />
        <Tab
          value="multi-agent"
          icon={<AutoAwesome sx={{ fontSize: 20 }} />}
          iconPosition="start"
          label={t('multiAgentChat', 'Multi-Agent Chat')}
        />
      </StyledTabs>
    </TabContainer>
  );
};

export default ChatModeTabNavigation;
