"""URLpatternsforSyriandialectAPIendpoints"""from django.urlsimportpathfrom.syrian_dialect_viewsimport(SyrianDialectConfigViewSyrianDialectConversionViewSyrianAIResponseViewSyrianCulturalContextViewSyrianDialectTestViewSyrianDialectStatusView)urlpatterns=[#Syriandialectconfigurationpath('config/'SyrianDialectConfigView.as_view()name='syrian-dialect-config')#TextconversiontoSyriandialectpath('convert/'SyrianDialectConversionView.as_view()name='syrian-dialect-convert')#AIresponsewithSyriandialectpath('ai-response/'SyrianAIResponseView.as_view()name='syrian-ai-response')#Culturalcontextpath('cultural-context/'SyrianCulturalContextView.as_view()name='syrian-cultural-context')#Testingendpointspath('test/'SyrianDialectTestView.as_view()name='syrian-dialect-test')path('status/'SyrianDialectStatusView.as_view()name='syrian-dialect-status')]