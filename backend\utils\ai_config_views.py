"""UnifiedAIConfigurationViewsThismoduleprovidesconsolidatedviewsforallAIconfigurationmanagement.Replacesthescatteredconfigurationendpointsacrossdifferentapps."""import json
import loggingfromtypingimportDictAnyfromdatetimeimport datetimefrom django.httpimportJsonResponsefrom django.views.decorators.csrfimportcsrf_exemptfrom django.views.decorators.httpimport require_http_methodsfrom django.contrib.auth.decoratorsimportuser_passes_testfrom django.utilsimport timezonefromrest_framework.decoratorsimportapi_viewpermission_classesfromrest_framework.permissionsimportIsAdminUserAllowAnyfromrest_framework.responseimportResponsefrom.ai_config_managerimportai_config_managerlogger=logging.getLogger(__name__)defis_admin_user(user):"""Checkifuserisadmin"""returnuser.is_authenticatedanduser.is_staff@csrf_exempt@require_http_methods(["GET""POST"])#@user_passes_test(is_admin_user)#Temporarilydisabledfortestingdefai_config_view(request):"""UnifiedAIConfigurationendpoint.GET:RetrievecurrentconfigurationPOST:Updateconfiguration"""ifrequest.method=='GET':try:config=ai_config_manager.get_unified_config()#Getactualadmininformationfromtherequestandsystemadmin_info={'username':'admin'#Defaultfallback'role':'administrator''lastLogin':'Unknown''totalRequests':0'totalTokens':0'averageTokensPerRequest':0}#Ifwehaveauserintherequestgetactualinfoifhasattr(request'user')andrequest.user.is_authenticated:admin_info['username']=request.user.usernameorrequest.user.emailor'admin'admin_info['lastLogin']=request.user.last_login.isoformat()ifrequest.user.last_loginelse'Never'admin_info['role']='superuser'ifgetattr(request.user'is_superuser'False)else'administrator'#Getactualusagestatisticsfrommonitoringtry:from.monitoringimportget_monitoring_metricsmetrics=get_monitoring_metrics()admin_info['totalRequests']=metrics.get('total_requests'0)admin_info['totalTokens']=metrics.get('total_tokens'0)admin_info['averageTokensPerRequest']=metrics.get('average_tokens_per_request'0)exceptExceptionase:logger.warning(f"Couldnotloadusagestatistics:{e}")#Keepthedefaultvalues#GetdynamicmodelinformationfromtheAIservicetry:from.ai.servicesimportget_ai_serviceai_service=get_ai_service()ifai_service:current_config=ai_service.get_current_configuration()model_info={'currentModel':current_config.get('model'config.get('default_model''gemini-2.0-flash'))'availableModels':['gemini-2.0-flash''gemini-1.5-pro''gemini-1.5-flash''gemini-pro']'fallbackModel':config.get('fallback_model''gemini-1.5-pro')'temperature':current_config.get('temperature'config.get('temperature'0.7))'maxTokens':current_config.get('max_tokens'config.get('max_tokens'2048))'timeout':config.get('timeout'30000)'retries':config.get('retries'3)'serviceAvailable':current_config.get('service_available'False)'apiKeyConfigured':current_config.get('api_key_configured'False)}else:#FallbackifAIserviceisnotavailablemodel_info={'currentModel':config.get('default_model''gemini-2.0-flash')'availableModels':['gemini-2.0-flash''gemini-1.5-pro''gemini-1.5-flash''gemini-pro']'fallbackModel':config.get('fallback_model''gemini-1.5-pro')'temperature':config.get('temperature'0.7)'maxTokens':config.get('max_tokens'2048)'timeout':config.get('timeout'30000)'retries':config.get('retries'3)'serviceAvailable':False'apiKeyConfigured':False}exceptExceptionase:logger.warning(f"CouldnotgetmodelinfofromAIservice:{e}")#Fallbackmodelinfomodel_info={'currentModel':config.get('default_model''gemini-2.0-flash')'availableModels':['gemini-2.0-flash''gemini-1.5-pro''gemini-1.5-flash''gemini-pro']'fallbackModel':config.get('fallback_model''gemini-1.5-pro')'temperature':config.get('temperature'0.7)'maxTokens':config.get('max_tokens'2048)'timeout':config.get('timeout'30000)'retries':config.get('retries'3)'serviceAvailable':False'apiKeyConfigured':False}returnJsonResponse({'status':'success''config':config'admin_info':admin_info'model_info':model_info'message':'Configurationretrievedsuccessfully'})exceptExceptionase:logger.error(f"ErrorretrievingAIconfig:{e}")returnJsonResponse({'status':'error''message':f'Failedtoretrieveconfiguration:{str(e)}'}status=500)elifrequest.method=='POST':try:data=json.loads(request.body)logger.info(f"ReceivedPOSTdata:{data}")#Extractconfigfromtherequestdataifit'swrappedconfig_data=data.get('config'data)logger.info(f"Usingconfigdataforupdate:{config_data}")#Usetheunifiedconfigmanagertoupdateconfigurationsuccess=ai_config_manager.update_configuration(config_datasource="admin_panel")ifsuccess:#ForcerefreshofAIservicesafterconfigurationupdatetry:from.ai.servicesimport refresh_ai_servicerefresh_ai_service()logger.info("AIservicerefreshedafterconfigurationupdate")exceptExceptionase:logger.warning(f"CouldnotrefreshAIservice:{e}")returnJsonResponse({'status':'success''message':'Configurationupdatedsuccessfully'})else:returnJsonResponse({'status':'error''message':'Failedtoupdateconfiguration'}status=400)exceptjson.JSONDecodeError:returnJsonResponse({'status':'error''message':'InvalidJSONdata'}status=400)exceptExceptionase:logger.error(f"ErrorupdatingAIconfig:{e}")returnJsonResponse({'status':'error''message':f'Failedtoupdateconfiguration:{str(e)}'}status=500)@csrf_exempt@require_http_methods(["GET""POST"])@user_passes_test(is_admin_user)defservice_config_view(requestservice_name):"""Service-specificconfigurationendpoint.GET:RetrieveserviceconfigurationPOST:Updateserviceconfiguration"""ifrequest.method=='GET':try:config=ai_config_manager.get_service_config(service_name)returnJsonResponse({'status':'success''service':service_name'config':config'message':f'Configurationfor{service_name}retrievedsuccessfully'})exceptExceptionase:logger.error(f"Errorretrievingconfigfor{service_name}:{e}")returnJsonResponse({'status':'error''message':f'Failedtoretrieveconfigurationfor{service_name}:{str(e)}'}status=500)elifrequest.method=='POST':try:data=json.loads(request.body)success=ai_config_manager.update_service_config(service_namedata)ifsuccess:returnJsonResponse({'status':'success''message':f'Configurationfor{service_name}updatedsuccessfully'})else:returnJsonResponse({'status':'error''message':f'Failedtoupdateconfigurationfor{service_name}'}status=400)exceptjson.JSONDecodeError:returnJsonResponse({'status':'error''message':'InvalidJSONdata'}status=400)exceptExceptionase:logger.error(f"Errorupdatingconfigfor{service_name}:{e}")returnJsonResponse({'status':'error''message':f'Failedtoupdateconfigurationfor{service_name}:{str(e)}'}status=500)@csrf_exempt@require_http_methods(["GET"])@user_passes_test(is_admin_user)defhealth_check_view(request):"""HealthcheckendpointforAIservices."""try:config=ai_config_manager.get_unified_config()#CheckAPIkeystatusfrom.api_key_serviceimportApiKeyServiceapi_key_status=ApiKeyService.get_key_status()#Checkservicestatustry:from.ai.servicesimportget_ai_serviceai_service=get_ai_service()service_status=ai_service.get_service_status()ifai_serviceelse{'status':'unavailable'}exceptExceptionase:service_status={'status':'error''error':str(e)}returnJsonResponse({'status':'success''health':{'api_key':api_key_status'services':service_status'config_version':config.get('version'0)'last_updated':config.get('updated_at''Never')}'message':'Healthcheckcompleted'})exceptExceptionase:logger.error(f"Errorinhealthcheck:{e}")returnJsonResponse({'status':'error''message':f'Healthcheckfailed:{str(e)}'}status=500)@csrf_exempt@require_http_methods(["GET"])@user_passes_test(is_admin_user)defusage_stats_view(request):"""Usagestatisticsendpoint."""try:#Getusagestatisticsfrommonitoringtry:from.monitoringimportget_monitoring_metricsstats=get_monitoring_metrics()exceptExceptionase:logger.warning(f"Couldnotloadmonitoringmetrics:{e}")stats={'total_requests':0'total_tokens':0'average_tokens_per_request':0'requests_last_24h':0'tokens_last_24h':0}returnJsonResponse({'status':'success''stats':stats'message':'Usagestatisticsretrievedsuccessfully'})exceptExceptionase:logger.error(f"Errorretrievingusagestats:{e}")returnJsonResponse({'status':'error''message':f'Failedtoretrieveusagestatistics:{str(e)}'}status=500)@csrf_exempt@require_http_methods(["GET"])@user_passes_test(is_admin_user)defmodels_view(request):"""Availablemodelsendpoint."""try:config=ai_config_manager.get_unified_config()#GetavailablemodelsfromAIservicetry:from.ai.servicesimportget_ai_serviceai_service=get_ai_service()ifai_service:available_models=ai_service.get_available_models()else:available_models=['gemini-2.0-flash''gemini-1.5-pro''gemini-1.5-flash''gemini-pro']exceptExceptionase:logger.warning(f"CouldnotgetmodelsfromAIservice:{e}")available_models=['gemini-2.0-flash''gemini-1.5-pro''gemini-1.5-flash''gemini-pro']returnJsonResponse({'status':'success''models':{'available':available_models'current':config.get('default_model''gemini-2.0-flash')'fallback':config.get('fallback_model''gemini-1.5-pro')}'message':'Modelsinformationretrievedsuccessfully'})exceptExceptionase:logger.error(f"Errorretrievingmodelsinfo:{e}")returnJsonResponse({'status':'error''message':f'Failedtoretrievemodelsinformation:{str(e)}'}status=500)@csrf_exempt@require_http_methods(["POST"])@user_passes_test(is_admin_user)defreset_config_view(request):"""Resetconfigurationtodefaults."""try:#Resettodefaultconfigurationsuccess=ai_config_manager.reset_to_defaults()ifsuccess:returnJsonResponse({'status':'success''message':'Configurationresettodefaultssuccessfully'})else:returnJsonResponse({'status':'error''message':'Failedtoresetconfiguration'}status=400)exceptExceptionase:logger.error(f"Errorresettingconfig:{e}")returnJsonResponse({'status':'error''message':f'Failedtoresetconfiguration:{str(e)}'}status=500)@csrf_exempt@require_http_methods(["GET"])@user_passes_test(is_admin_user)defexport_config_view(request):"""Exportcurrentconfiguration."""try:config=ai_config_manager.get_unified_config()#Createexportdataexport_data={'config':config'exported_at':datetime.now().isoformat()'version':config.get('version'0)}returnJsonResponse({'status':'success''export':export_data'message':'Configurationexportedsuccessfully'})exceptExceptionase:logger.error(f"Errorexportingconfig:{e}")returnJsonResponse({'status':'error''message':f'Failedtoexportconfiguration:{str(e)}'}status=500)#DirectAIendpointsfortesting/frontenduse@csrf_exempt@require_http_methods(["POST"])defchat_endpoint(request):"""DirectAIchatendpoint"""try:from.ai.servicesimportget_ai_servicedata=json.loads(request.body)message=data.get('message''')conversation_id=data.get('conversation_id')ifnotmessage:returnJsonResponse({'status':'error''message':'Nomessageprovided'}status=400)ai_service=get_ai_service()ifnotai_service:returnJsonResponse({'status':'error''message':'AIservicenotavailable'}status=503)#UsetheSyriandialectresponsemethodforArabicsupportresponse_data=ai_service.generate_syrian_response(messagesubject='general'user_level='intermediate'prefer_syrian=True)#FormatresponsetomatchfrontendChatResponseinterfaceformatted_response={'conversation_id':conversation_idor1'content':response_data.get('content'response_data.get('answer''عذراً،ماقدرتأفهمسؤالك.ممكنتعيدصياغته؟'))'metadata':{'is_arabic':response_data.get('is_arabic'False)'is_syrian_dialect':response_data.get('is_syrian_dialect'False)'cultural_context_applied':response_data.get('cultural_context_applied'False)'model_used':response_data.get('model_used''unknown')'format_recommendations':{'detail_level':'medium''step_by_step':True'include_examples':True'use_visual_aids':False'chunk_size':500'interaction_style':'conversational'}'learning_indicators':{'visual_preference':0.7'example_preference':0.8'step_preference':0.9'detail_level':0.7'interaction_style':0.8'topic_complexity':0.6'followup_questions':0.7}}'suggested_questions':response_data.get('suggested_questions'["ممكنتشرحليأكتر؟""شوأمثلةعلىهالشي؟""كيفبطبقهالشيعملياً؟"])}returnJsonResponse(formatted_response)exceptExceptionase:logger.error(f"Errorinchatendpoint:{e}")returnJsonResponse({'status':'error''message':f'Failedtogenerateresponse:{str(e)}'}status=500)@csrf_exempt@require_http_methods(["POST"])defassistant_endpoint(request):"""DirectAIassistantendpoint"""try:from.ai.servicesimportget_ai_servicedata=json.loads(request.body)query=data.get('query''')context=data.get('context'{})ifnotquery:returnJsonResponse({'status':'error''message':'Noqueryprovided'}status=400)ai_service=get_ai_service()ifnotai_service:returnJsonResponse({'status':'error''message':'AIservicenotavailable'}status=503)response=ai_service.generate_study_response(querycontext)returnJsonResponse({'status':'success''response':response'message':'Assistantresponsegeneratedsuccessfully'})exceptExceptionase:logger.error(f"Errorinassistantendpoint:{e}")returnJsonResponse({'status':'error''message':f'Failedtogenerateassistantresponse:{str(e)}'}status=500)@csrf_exempt@require_http_methods(["POST"])deftutor_endpoint(request):"""DirectAItutorendpoint"""try:from.ai.servicesimportget_ai_servicedata=json.loads(request.body)question=data.get('question''')subject=data.get('subject''')difficulty=data.get('difficulty''intermediate')ifnotquestion:returnJsonResponse({'status':'error''message':'Noquestionprovided'}status=400)ai_service=get_ai_service()ifnotai_service:returnJsonResponse({'status':'error''message':'AIservicenotavailable'}status=503)response=ai_service.generate_tutoring_response(questionsubjectdifficulty)returnJsonResponse({'status':'success''response':response'message':'Tutoringresponsegeneratedsuccessfully'})exceptExceptionase:logger.error(f"Errorintutorendpoint:{e}")returnJsonResponse({'status':'error''message':f'Failedtogeneratetutoringresponse:{str(e)}'}status=500)#Conversationmanagementendpoints@csrf_exempt@require_http_methods(["GET""POST"])defconversations_endpoint(request):"""HandlechatconversationsGET:ListallconversationsfortheuserPOST:Createanewconversation"""try:from chatbot.modelsimportChatConversationfrom django.contrib.authimportget_user_model#FornowuseadefaultuserifnoauthenticationUser=get_user_model()user=Noneifhasattr(request'user')andrequest.user.is_authenticated:user=request.userelse:#Getorcreateadefaultuserfortestingusercreated=User.objects.get_or_create(username='default_user'defaults={'email':'<EMAIL>''first_name':'Default''last_name':'User'})ifrequest.method=='GET':#Getrealconversationsfromdatabaseconversations=ChatConversation.objects.filter(user=user).order_by('-updated_at')conversations_data=[]forconvinconversations:conversations_data.append({'id':conv.id'title':conv.titleor'NewConversation''created_at':conv.created_at.isoformat()'updated_at':conv.updated_at.isoformat()'message_count':conv.messages.count()})returnJsonResponse(conversations_datasafe=False)elifrequest.method=='POST':#Createanewconversationdata=json.loads(request.body)title=data.get('title''ChatGPT')#DefaulttoChatGPTlikethepopularAIassistant#Createrealconversationindatabasenew_conversation=ChatConversation.objects.create(user=usertitle=titleuser_role=getattr(user'role''STUDENT'))returnJsonResponse({'id':new_conversation.id'title':new_conversation.title'created_at':new_conversation.created_at.isoformat()'updated_at':new_conversation.updated_at.isoformat()'message_count':0}status=201)exceptExceptionase:logger.error(f"Errorinconversations_endpoint:{e}")returnJsonResponse({'status':'error''message':f'Failedtohandleconversationrequest:{str(e)}'}status=500)@csrf_exempt@require_http_methods(["GET""DELETE""PATCH"])defconversation_detail_endpoint(requestconversation_id):"""HandleindividualconversationoperationsGET:GetconversationdetailswithmessagesDELETE:DeleteaconversationPATCH:Updateconversation(e.g.title)"""try:from chatbot.modelsimportChatConversationChatMessagefrom django.contrib.authimportget_user_modelfrom django.shortcutsimportget_object_or_404#FornowuseadefaultuserifnoauthenticationUser=get_user_model()user=Noneifhasattr(request'user')andrequest.user.is_authenticated:user=request.userelse:#Getorcreateadefaultuserfortestingusercreated=User.objects.get_or_create(username='default_user'defaults={'email':'<EMAIL>''first_name':'Default''last_name':'User'})ifrequest.method=='GET':#Getrealconversationwithmessagesfromdatabasetry:conversation=get_object_or_404(ChatConversationid=conversation_iduser=user)messages=conversation.messages.all().order_by('timestamp')messages_data=[]formsginmessages:messages_data.append({'id':msg.id'content':msg.content'role':msg.role'timestamp':msg.timestamp.isoformat()})conversation_data={'id':conversation.id'title':conversation.title'created_at':conversation.created_at.isoformat()'updated_at':conversation.updated_at.isoformat()'messages':messages_data}returnJsonResponse(conversation_data)exceptChatConversation.DoesNotExist:returnJsonResponse({'status':'error''message':'Conversationnotfound'}status=404)elifrequest.method=='DELETE':#Deleterealconversationfromdatabasetry:conversation=get_object_or_404(ChatConversationid=conversation_iduser=user)conversation.delete()returnJsonResponse({'status':'success''message':'Conversationdeleted'})exceptChatConversation.DoesNotExist:returnJsonResponse({'status':'error''message':'Conversationnotfound'}status=404)elifrequest.method=='PATCH':data=json.loads(request.body)title=data.get('title')iftitle:try:conversation=get_object_or_404(ChatConversationid=conversation_iduser=user)conversation.title=titleconversation.save()updated_conversation={'id':conversation.id'title':conversation.title'updated_at':conversation.updated_at.isoformat()}returnJsonResponse(updated_conversation)exceptChatConversation.DoesNotExist:returnJsonResponse({'status':'error''message':'Conversationnotfound'}status=404)else:returnJsonResponse({'status':'error''message':'Titleisrequired'}status=400)exceptExceptionase:logger.error(f"Errorinconversation_detail_endpoint:{e}")returnJsonResponse({'status':'error''message':f'Failedtohandleconversationrequest:{str(e)}'}status=500)#Multi-agentchatendpoint@csrf_exempt@require_http_methods(["POST"])defmulti_agent_chat_endpoint(request):"""Multi-agentchatendpointforspecializedAIagents"""try:from.ai.servicesimportget_ai_servicedata=json.loads(request.body)message=data.get('message''')agent_type=data.get('agent_type''general')conversation_id=data.get('conversation_id')ifnotmessage:returnJsonResponse({'status':'error''message':'Nomessageprovided'}status=400)ai_service=get_ai_service()ifnotai_service:returnJsonResponse({'status':'error''message':'AIservicenotavailable'}status=503)#Customizeresponsebasedonagenttypeagent_context={'conversation_id':conversation_id'agent_type':agent_type'specialized_mode':True}#Addagent-specificcontextifagent_type=='math_tutor':agent_context['context']='Youareaspecializedmathtutor.Focusonmathematicalconceptsproblem-solvingandstep-by-stepexplanations.'elifagent_type=='science_tutor':agent_context['context']='Youareaspecializedsciencetutor.Focusonscientificconceptsexperimentsandevidence-basedexplanations.'elifagent_type=='language_tutor':agent_context['context']='Youareaspecializedlanguagetutor.Focusongrammarvocabularyliteratureandwritingskills.'elifagent_type=='advisor':agent_context['context']='Youareacareeradvisor.Focusoncareerguidanceacademicplanningandprofessionaldevelopment.'elifagent_type=='assessor':agent_context['context']='Youareanassessmentspecialist.Focusonevaluatingknowledgecreatingquizzesandprovidingfeedback.'elifagent_type=='content_creator':agent_context['context']='Youareacontentcreator.Focusongeneratingeducationalmaterialslessonplansandlearningresources.'else:#generaltutoragent_context['context']='Youareageneraltutor.Providecomprehensiveeducationalsupportacrossvarioussubjects.'#UseSyriandialectresponseforbetterArabicsupportresponse_data=ai_service.generate_syrian_response(messagesubject=agent_type.replace('_tutor''').replace('_''')user_level='intermediate'prefer_syrian=True)#Formatresponseformulti-agentchatformatted_response={'conversation_id':conversation_idor1'agent_type':agent_type'content':response_data.get('content'response_data.get('answer'''))'metadata':{'agent_info':{'type':agent_type'name':{'math_tutor':'أستاذالرياضيات''science_tutor':'أستاذالعلوم''language_tutor':'أستاذاللغة''advisor':'المستشارالمهني''assessor':'المقيّم''content_creator':'منشئالمحتوى''tutor':'الأستاذالعام'}.get(agent_type'المساعدالذكي')'icon':{'math_tutor':'🔢''science_tutor':'🔬''language_tutor':'📝''advisor':'🎯''assessor':'📊''content_creator':'✨''tutor':'🎓'}.get(agent_type'🤖')}'specialized_features':response_data.get('specialized_features'{})'learning_indicators':response_data.get('learning_indicators'{})}'suggested_questions':response_data.get('suggested_questions'["ممكنتشرحليبطريقةتانية؟""شوالخطوةالجاية؟""ممكنتعطينيمثال؟"])}returnJsonResponse(formatted_response)exceptExceptionase:logger.error(f"Errorinmulti-agentchatendpoint:{e}")returnJsonResponse({'status':'error''message':f'Failedtogenerateresponse:{str(e)}'}status=500)@api_view(['GET'])@permission_classes([AllowAny])defai_health_check(request):"""GetcomprehensiveAIservicehealthstatus"""try:import timefrom utils.ai.servicesimportget_ai_serviceai_service=get_ai_service()ifnotai_service:returnJsonResponse({'status':'unhealthy''error':'AIservicenotavailable''timestamp':time.time()}status=503)health_status=ai_service.get_health_status()#ReturnappropriateHTTPstatusbasedonhealthifhealth_status['status']=='healthy':status_code=200elifhealth_status['status']=='degraded':status_code=200#Stillfunctionalbutwithwarningselse:#unhealthystatus_code=503#ServiceunavailablereturnJsonResponse(health_statusstatus=status_code)exceptExceptionase:logger.error(f"ErrorinAIhealthcheck:{e}")returnJsonResponse({'status':'unhealthy''error':'Healthcheckfailed''details':str(e)'timestamp':time.time()}status=500)